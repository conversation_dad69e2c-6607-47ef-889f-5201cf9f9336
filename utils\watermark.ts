// 水印处理工具函数
export async function addWatermarkToImage(imageUrl: string): Promise<string> {
  return new Promise((resolve, reject) => {
    console.log('🖼️ Loading image for watermark:', imageUrl.substring(0, 50) + '...');

    // 首先检查是否需要水印处理
    shouldAddWatermark().then(needsWatermark => {
      if (needsWatermark) {
        // 用户积分不足，需要添加水印
        loadImageAndAddWatermark(imageUrl, resolve, reject);
        return;
      } else {
        // 用户积分足够，直接返回原图
        resolve(imageUrl);
      }
    }).catch(error => {
      // 如果检查失败，为安全起见添加水印
      loadImageAndAddWatermark(imageUrl, resolve, reject);
    });
  });
}

// 检查用户积分并决定是否需要添加水印
export async function shouldAddWatermark(): Promise<boolean> {
  try {
    console.log('🔍 Checking user credits for watermark...');
    const response = await fetch('/api/credits');
    if (response.ok) {
      const userCredits = await response.json();
      console.log('💰 User credits:', userCredits.left_credits);
      const needsWatermark = userCredits.left_credits <= 5;
      console.log('🎨 Needs watermark:', needsWatermark);
      return needsWatermark;
    }
    console.warn('⚠️ Credits API response not ok, defaulting to add watermark');
    return true; // 如果获取积分失败，默认添加水印
  } catch (error) {
    console.error('❌ Error checking user credits:', error);
    return true; // 如果获取积分失败，默认添加水印
  }
}

// 处理图片数组，根据用户积分和用户选择添加水印
export async function processImagesWithWatermark(imageUrls: string[], taskId?: string, userWantsWatermark?: boolean): Promise<string[]> {
  console.log('🖼️ Processing images with watermark check...', imageUrls.length, 'images');
  const needsWatermark = await shouldAddWatermark();

  // 如果用户积分不足，强制添加水印（忽略用户选择）
  if (needsWatermark) {
    console.log('🎨 User has insufficient credits (≤ 5), adding watermark automatically');
  } else {
    // 用户积分充足，根据用户选择决定
    if (userWantsWatermark === true) {
      console.log('🎨 User has sufficient credits but chose to add watermark');
    } else {
      console.log('✅ User has sufficient credits (> 5), returning original images without watermark');
      return imageUrls; // 用户积分足够且不想要水印，返回原图
    }
  }

  // 串行处理图片，确保数据库更新的顺序
  const watermarkedImages: string[] = [];

  for (let index = 0; index < imageUrls.length; index++) {
    const imageUrl = imageUrls[index];
    try {
      const watermarkedImage = await addWatermarkToImage(imageUrl);

      // 如果有taskId，上传水印图片到数据库
      if (taskId) {
        try {
          console.log(`💾 Uploading watermarked image ${index + 1} to database...`);
          const uploadResponse = await fetch('/api/upload-watermarked-image', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              imageData: watermarkedImage,
              taskId: taskId,
              originalUrl: imageUrl,
              imageIndex: index
            }),
          });

          if (uploadResponse.ok) {
            const result = await uploadResponse.json();
            console.log(`✅ Watermarked image ${index + 1} uploaded to database:`, result.watermarkedUrl);
            watermarkedImages.push(result.watermarkedUrl);
          } else {
            watermarkedImages.push(watermarkedImage);
          }
        } catch (uploadError) {
          console.error(`❌ Error uploading watermarked image ${index + 1}:`, uploadError);
          watermarkedImages.push(watermarkedImage);
        }
      } else {
        // 没有taskId，直接返回base64
        watermarkedImages.push(watermarkedImage);
      }

    } catch (error) {
      watermarkedImages.push(imageUrl); // 如果添加水印失败，返回原图
    }
  }

  console.log('🎉 Watermark processing and upload completed');
  return watermarkedImages;
}

// 加载图片并添加水印的核心函数
function loadImageAndAddWatermark(imageUrl: string, resolve: (value: string) => void, reject: (reason?: any) => void) {
  // 检查是否需要使用代理 - 对于跨域图片总是使用代理
  const needsProxy = imageUrl.startsWith('https://img.kontext-dev.com/') ||
                     imageUrl.startsWith('https://your-domain.com/') ||
                     imageUrl.startsWith('https://') && !imageUrl.includes(window.location.hostname);

  let finalImageUrl = imageUrl;
  if (needsProxy) {
    // 使用代理API来避免跨域问题
    finalImageUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}&download=false`;
  }

  const img = new Image();

  // 设置跨域策略
  if (!needsProxy) {
    img.crossOrigin = 'anonymous';
  }

  img.onload = function() {
    console.log('✅ Image loaded successfully, adding watermark...');
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        createWatermarkPlaceholder(imageUrl, resolve, reject);
        return;
      }

      canvas.width = img.width;
      canvas.height = img.height;

      // 绘制原始图片
      ctx.drawImage(img, 0, 0);

      // 添加水印
      addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'restore-old-photos.com');

      // 返回带水印的base64图片
      const watermarkedData = canvas.toDataURL('image/png');
      resolve(watermarkedData);

    } catch (error) {
      createWatermarkPlaceholder(imageUrl, resolve, reject);
    }
  };

  img.onerror = function(error) {
    if (needsProxy) {
      // 如果代理失败，尝试直接加载
      const directImg = new Image();
      directImg.crossOrigin = 'anonymous';
      directImg.onload = img.onload;
      directImg.onerror = function() {
        createWatermarkPlaceholder(imageUrl, resolve, reject);
      };
      directImg.src = imageUrl;
    } else {
      createWatermarkPlaceholder(imageUrl, resolve, reject);
    }
  };

  // 尝试加载图片
  img.src = finalImageUrl;
}

// 在Canvas上添加水印的函数
function addWatermarkToCanvas(ctx: CanvasRenderingContext2D, width: number, height: number, text: string) {
  // 保存当前状态
  ctx.save();

  // 设置水印样式，字体大小根据图片尺寸自适应
  const fontSize = Math.max(width, height) * 0.025; // 图片尺寸的2.5%
  ctx.font = `bold ${fontSize}px Arial`;
  ctx.fillStyle = 'rgba(255, 255, 255, 0.7)'; // 白色，70%透明度
  ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)'; // 黑色边框，50%透明度
  ctx.lineWidth = 2;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';

  // 计算水印间距
  const baseSpacing = 300;
  const spacingX = Math.max(baseSpacing, width * 0.25);  // 宽度的25%，最小300px
  const spacingY = Math.max(baseSpacing * 0.7, height * 0.2); // 高度的20%，最小210px

  // 旋转角度（-45度）
  const angle = -Math.PI / 4;

  // 计算网格数量
  const cols = Math.ceil(width / spacingX) + 3;
  const rows = Math.ceil(height / spacingY) + 3;

  // 计算起始偏移量，使水印居中
  const startX = -(cols * spacingX - width) / 2;
  const startY = -(rows * spacingY - height) / 2;

  // 在网格中添加水印
  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < cols; col++) {
      ctx.save();

      const x = startX + col * spacingX;
      const y = startY + row * spacingY;

      // 只在合理范围内绘制水印
      if (x > -spacingX && x < width + spacingX &&
          y > -spacingY && y < height + spacingY) {

        ctx.translate(x, y);
        ctx.rotate(angle);

        ctx.strokeText(text, 0, 0);
        ctx.fillText(text, 0, 0);
      }

      ctx.restore();
    }
  }

  // 恢复状态
  ctx.restore();
}

// 创建水印占位符（当图片加载失败时使用）
function createWatermarkPlaceholder(imageUrl: string, resolve: (value: string) => void, reject: (reason?: any) => void) {
  console.log('🎨 Creating watermark placeholder for:', imageUrl.substring(0, 50) + '...');

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    reject(new Error('Failed to get canvas context'));
    return;
  }

  // 设置标准尺寸
  canvas.width = 1024;
  canvas.height = 1024;

  // 创建渐变背景
  const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
  gradient.addColorStop(0, '#f0f0f0');
  gradient.addColorStop(1, '#e0e0e0');
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // 添加水印
  addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'restore-old-photos.com');

  // 添加提示文字
  ctx.save();
  ctx.font = 'bold 32px Arial';
  ctx.fillStyle = 'rgba(100, 100, 100, 0.8)';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('Watermarked Image', canvas.width / 2, canvas.height / 2);
  ctx.restore();

  const watermarkedData = canvas.toDataURL('image/png');
  console.log('✅ Watermark placeholder created');
  resolve(watermarkedData);
}
