"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';
import { processImagesWithWatermark } from '@/utils/watermark';

import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { X, Upload, HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export const Kontext = () => {
  // 会话和挂载状态
  const { data: session } = useSession();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [userCredits, setUserCredits] = useState<number | null>(null);

  // 图片和处理状态
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [prompt, setPrompt] = useState<string>("");
  const [aspectRatio, setAspectRatio] = useState<string>('1:1');
  const [isProcessing, setIsProcessing] = useState(false);
  const [taskId, setTaskId] = useState<string | null>(null);
  const [status, setStatus] = useState<'IDLE' | 'UPLOADING' | 'GENERATING' | 'COMPLETED' | 'ERROR'>('IDLE');
  const [perceptionProgress, setPerceptionProgress] = useState(0);
  const [requiredCredits, setRequiredCredits] = useState<number>(3); // Default required credits
  const [progress, setProgress] = useState<number>(0);

  // 结果状态
  const [result, setResult] = useState<{
    originalImageUrl?: string;
    generatedImageUrl?: string;
    r2ImageUrl?: string; // 新增R2图片URL字段
  }>({});

  // 水印相关状态
  const [showWatermark, setShowWatermark] = useState(true);
  const [userNeedsWatermark, setUserNeedsWatermark] = useState(true);
  const [isUserPro, setIsUserPro] = useState(false);

  // 轮询间隔
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 组件加载时检查会话状态
  useEffect(() => {
    setMounted(true);
    if (session) {
      fetchUserCredits();
    }
  }, [session]);

  // 检查用户积分并设置水印状态
  useEffect(() => {
    if (userCredits !== null) {
      const needsWatermark = userCredits <= 5;
      setUserNeedsWatermark(needsWatermark);

      // 如果用户积分不足，强制开启水印
      if (needsWatermark) {
        setShowWatermark(true);
        setIsUserPro(false);
        console.log(`🎨 User credits: ${userCredits} ≤ 5, watermark forced ON`);
      } else {
        // 积分充足的用户，默认关闭水印，但可以控制
        setShowWatermark(false);
        setIsUserPro(true);
        console.log(`✅ User credits: ${userCredits} > 5, watermark OFF by default (user can control)`);
      }
    } else {
      // 如果积分为null（未登录或获取失败），默认需要水印
      setUserNeedsWatermark(true);
      setShowWatermark(true);
      setIsUserPro(false);
      console.log('⚠️ User credits unknown, watermark forced ON');
    }
  }, [userCredits]);

  // 确保未登录用户也有正确的水印状态
  useEffect(() => {
    if (!session) {
      setUserNeedsWatermark(true);
      setShowWatermark(true);
      setIsUserPro(false);
      console.log('🔒 User not logged in, watermark forced ON');
    }
  }, [session]);

  // 获取用户积分
  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/credits');
      if (!response.ok) {
        throw new Error('Failed to fetch user credits');
      }
      const data = await response.json();
      console.log('💰 Fetched user credits:', data.left_credits);
      setUserCredits(data.left_credits);
    } catch (error) {
      console.error('❌ Error fetching user credits:', error);
      // 获取积分失败时，为安全起见，强制开启水印
      setUserCredits(0); // 设置为0确保水印开启
      setUserNeedsWatermark(true);
      setShowWatermark(true);
      setIsUserPro(false);
    }
  };

  // 生成成功后刷新积分
  useEffect(() => {
    if (result.generatedImageUrl && session) {
      //console.log("图像生成成功，立即刷新积分");
      fetchUserCredits();
    }
  }, [result.generatedImageUrl, session]);

  // 当真实进度更新时，同步到感知进度
  useEffect(() => {
    if (progress > perceptionProgress) {
      setPerceptionProgress(progress);
    }
  }, [progress]);

  // 感知进度模拟
  useEffect(() => {
    if (isProcessing && status === 'GENERATING') {
      // 立即提供反馈
      const initialProgress = Math.max(perceptionProgress, 5);
      if (perceptionProgress < initialProgress) {
        setPerceptionProgress(initialProgress);
      }
      
      // 创建模拟进度，但确保不会回退
      const simulatedInterval = setInterval(() => {
        setPerceptionProgress(prev => {
          // 确保不会超过实际进度太多，但也不会回退
          const maxProgress = Math.min(Math.max(progress + 5, 95), 99); // 最高不超过99%，除非实际进度已经达到或超过95%
          
          // 根据当前进度值计算增量，进度越高增量越小
          let increment = 0;
          if (prev < 30) increment = 0.8;
          else if (prev < 60) increment = 0.5;
          else if (prev < 80) increment = 0.3;
          else if (prev < 90) increment = 0.1;
          else increment = 0.05;
          
          // 计算新的进度值，但不超过maxProgress
          const newProgress = Math.min(prev + increment, maxProgress);
          
          // 确保进度只增不减
          return Math.max(prev, newProgress);
        });
      }, 1000);
      
      return () => clearInterval(simulatedInterval);
    }
  }, [isProcessing, status, progress, perceptionProgress]);

  // 轮询任务状态
  useEffect(() => {
    if (taskId && status === 'GENERATING') {
      // 添加超时，在合理时间（10分钟）后停止轮询
      let pollingTimeout: NodeJS.Timeout | undefined = undefined;
      
      // 跟踪上次进度更新的时间
      let lastProgressUpdateTime = Date.now();
      
      const stopPolling = (interval: NodeJS.Timeout) => {
        clearInterval(interval);
        if (pollingTimeout) clearTimeout(pollingTimeout);
        
        // 如果超时后仍在处理中且没有错误，显示错误信息
        if (isProcessing && !error) {
          resetProcessingState("Processing timed out. Please try again or use a different image.");
          toast.error("Processing timed out");
        }
      };
      
      // 设置10分钟后停止轮询
      pollingTimeout = setTimeout(() => {
        console.log("Polling timeout after 10 minutes");
        if (pollingInterval) stopPolling(pollingInterval);
      }, 10 * 60 * 1000);

      // 检查任务是否失败的短间隔轮询
      const failureCheckInterval = setInterval(async () => {
        try {
          const timestamp = Date.now();
          const response = await fetch(`/api/kontext/record-info?taskId=${taskId}&_t=${timestamp}`, {
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });
          
          if (!response.ok) {
            // 处理非200状态码
            const statusCode = response.status;
            let errorMessage = "Unknown error occurred";
            
            switch (statusCode) {
              case 401:
                errorMessage = "Authentication failed. Please sign in again.";
                break;
              case 402:
                errorMessage = "Insufficient credits. Please recharge your account.";
                break;
              case 404:
                errorMessage = "Task not found. It may have been deleted or expired.";
                break;
              case 422:
                errorMessage = "Invalid request parameters. Please try again.";
                break;
              case 429:
                errorMessage = "Too many requests. Please slow down and try again later.";
                break;
              case 455:
                errorMessage = "Service is under maintenance. Please try again later.";
                break;
              case 500:
                errorMessage = "Server error occurred. Please try again later.";
                break;
              case 501:
                errorMessage = "Image generation failed. Please try with different settings.";
                break;
              case 505:
                errorMessage = "This feature is currently disabled. Please try again later.";
                break;
            }
            
            if (statusCode !== 429) { // 对于429错误，我们可以继续尝试
              clearInterval(failureCheckInterval);
              const currentPollingInterval = pollingInterval;
              if (currentPollingInterval) clearInterval(currentPollingInterval);
              if (pollingTimeout) clearTimeout(pollingTimeout);
              
              resetProcessingState(`Task processing failed: ${errorMessage}`);
              toast.error(errorMessage);
            }
            return;
          }
          
          const data = await safeParseJSON(response);
          
          // 提取状态信息 - 兼容三种格式: 
          // 1. 原始API格式 (code/msg/data)
          // 2. 我们的增强格式 (包含status和successFlag)
          // 3. Generator4o格式 (data对象包含status等)
          let currentStatus, successFlag, currentProgress, errorCode, errorMessage;
          
          // 检查是否是Kontext原始API响应格式
          if (data.code === 200 && data.msg === "success" && data.data) {
            const apiData = data.data;
            
            // 直接使用API返回的successFlag
            successFlag = apiData.successFlag;
            
            // 根据successFlag设置状态
            if (successFlag === 1) {
              currentStatus = "COMPLETED";
              currentProgress = "1.0"; // 100%
            } else if (successFlag === 3) {
              currentStatus = "FAILED";
              currentProgress = "0";
            } else {
              currentStatus = "GENERATING";
              currentProgress = "0.5"; // 默认50%
            }
            
            // 获取错误信息
            errorCode = apiData.errorCode;
            errorMessage = apiData.errorMessage;
            
            //console.log(`response: successFlag=${successFlag}, status=${currentStatus}, errorCode=${errorCode}`);
          } else {
            // 使用我们的增强格式
            currentStatus = data.status || data.data?.status;
            successFlag = data.data?.successFlag;
            currentProgress = data.progress || data.data?.progress;
            errorCode = data.data?.errorCode;
            errorMessage = data.error || data.data?.errorMessage;
          }
          
          // 检查失败状态 - 兼容两种格式
          if (currentStatus === "FAILED" || 
              currentStatus === "ERROR" || 
              currentStatus === "GENERATE_FAILED" || 
              successFlag === 3) {
            clearInterval(failureCheckInterval);
            const currentPollingInterval = pollingInterval;
            if (currentPollingInterval) clearInterval(currentPollingInterval);
            if (pollingTimeout) clearTimeout(pollingTimeout);
            
            // 获取详细错误信息
            let detailedError = errorMessage || "Unknown error";
            if (errorCode === 400) {
              if (errorMessage?.includes("violating content policies")) {
                detailedError = "Your content was flagged as violating content policies. Please try uploading a different image.";
              } else if (errorMessage?.includes("did not return the image")) {
                detailedError = "There are too many users at the moment. Please try again later.";
              }
            } else if (errorCode === 500 || errorCode === 501) {
              detailedError = "Image generation failed. Please try again later.";
            } else if (errorCode === 402) {
              detailedError = "Insufficient credits. Please recharge your account.";
            }
            
            resetProcessingState(`Task processing failed: ${detailedError}`);
            toast.error("Generation failed, please try again later");
          }
        } catch (error) {
          console.error("Error in quick failure check:", error);
        }
      }, 1000); // 每1秒检查一次失败状态

      // 主轮询间隔
      const interval = setInterval(async () => {
        try {
          const timestamp = Date.now();
          //console.log(`Polling task status, ID: ${taskId}, time: ${new Date().toISOString()}`);
          const response = await fetch(`/api/kontext/record-info?taskId=${taskId}&_t=${timestamp}`, {
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });
          
          if (!response.ok) {
            throw new Error("Failed to get task status");
          }
          
          const data = await safeParseJSON(response);
          //console.log("Polling response data:", data);

          // 提取状态信息 - 兼容三种格式: 
          // 1. 原始API格式 (code/msg/data)
          // 2. 我们的增强格式 (包含status和successFlag)
          // 3. Generator4o格式 (data对象包含status等)
          let currentStatus, successFlag, currentProgress, errorCode, errorMessage;
          
          // 检查是否是Kontext原始API响应格式
          if (data.code === 200 && data.msg === "success" && data.data) {
            const apiData = data.data;
            
            // 直接使用API返回的successFlag
            successFlag = apiData.successFlag;
            
            // 根据successFlag设置状态
            if (successFlag === 1) {
              currentStatus = "COMPLETED";
              currentProgress = "1.0"; // 100%
            } else if (successFlag === 3) {
              currentStatus = "FAILED";
              currentProgress = "0";
            } else {
              currentStatus = "GENERATING";
              currentProgress = "0.5"; // 默认50%
            }
            
            // 获取错误信息
            errorCode = apiData.errorCode;
            errorMessage = apiData.errorMessage;
            
            //console.log(`response: successFlag=${successFlag}, status=${currentStatus}, errorCode=${errorCode}`);
          } else {
            // 使用我们的增强格式
            currentStatus = data.status || data.data?.status;
            successFlag = data.data?.successFlag;
            currentProgress = data.progress || data.data?.progress;
            errorCode = data.data?.errorCode;
            errorMessage = data.error || data.data?.errorMessage;
          }
          
          // 更新状态，但避免不必要的更新
          const newStatus = currentStatus === "COMPLETED" || currentStatus === "SUCCESS" || successFlag === 1 ? 'COMPLETED' : 'GENERATING';
          if (status !== newStatus) {
            setStatus(newStatus);
          }
          
          // 计算进度 - 兼容两种格式
          const progressValue = parseFloat(currentProgress || "0");
          const newProgress = Math.round(progressValue * 100);
          
          // 确保进度只增不减
          if (newProgress > progress) {
            setProgress(newProgress);
            lastProgressUpdateTime = Date.now(); // 更新进度时更新时间
            //console.log(`Progress update: ${progress}% -> ${newProgress}%`);
          } else if (newProgress < progress) {
            //console.log(`Ignoring progress rollback: ${progress}% -> ${newProgress}%, keeping current progress: ${progress}%`);
          }
          
          //console.log(`Current status: ${currentStatus}, progress: ${progress}%`);

          // 计算自上次进度更新以来的分钟数
          const minutesSinceLastProgress = (Date.now() - lastProgressUpdateTime) / (60 * 1000);
          //console.log(`Minutes since last progress update: ${minutesSinceLastProgress.toFixed(2)}`);

          // 检查失败条件
          if (currentStatus === "FAILED" || 
              currentStatus === "ERROR" || 
              currentStatus === "GENERATE_FAILED" || 
              successFlag === 3) {
            // 清除所有轮询和超时
            clearInterval(interval);
            clearInterval(failureCheckInterval);
            if (pollingTimeout) clearTimeout(pollingTimeout);
            
            // 获取详细错误信息
            let detailedError = errorMessage || "Unknown error";
            if (errorCode === 400) {
              if (errorMessage?.includes("violating content policies")) {
                detailedError = "Your content was flagged as violating content policies. Please try uploading a different image.";
              } else if (errorMessage?.includes("did not return the image")) {
                detailedError = "There are too many users at the moment. Please try again later.";
              }
            } else if (errorCode === 500 || errorCode === 501) {
              detailedError = "Image generation failed. Please try again later.";
            } else if (errorCode === 402) {
              detailedError = "Insufficient credits. Please recharge your account.";
            }
            
            resetProcessingState(`Task processing failed: ${detailedError}`);
            toast.error("Generation failed, please try again later");
            return;
          }

          // 检查完成条件 - 兼容Kontext API格式和我们的增强格式
          const hasKontextResult = data.code === 200 && data.msg === "success" && 
                                 data.data?.response?.resultImageUrl;
          const hasOtherResult = (data.result?.images && data.result.images.length > 0) || 
                               (data.data?.response?.resultUrls && data.data.response.resultUrls.length > 0);
                               
          if ((currentStatus === "COMPLETED" || currentStatus === "SUCCESS" || successFlag === 1 ||
               currentStatus === "POSSIBLE_COMPLETED" && minutesSinceLastProgress > 60) && 
              (hasKontextResult || hasOtherResult)) {
            // 清除所有轮询和超时
            clearInterval(interval);
            clearInterval(failureCheckInterval);
            if (pollingTimeout) clearTimeout(pollingTimeout);
            
            // 获取生成的图像URL - 兼容多种格式
            let generatedImageUrl = '';
            
            // 检查是否是Kontext原始API响应格式
            if (data.code === 200 && data.msg === "success" && data.data) {
              const apiData = data.data;
              
              // 根据您提供的示例，首先检查response.resultImageUrl
              if (apiData.response && apiData.response.resultImageUrl) {
                currentStatus = "COMPLETED";
                currentProgress = "1.0"; // 100%
                generatedImageUrl = apiData.response.resultImageUrl;
                console.log('Got image URL from response.resultImageUrl:', generatedImageUrl);
              } 
              // 其次检查其他可能的位置
              else if (apiData.resultUrls && apiData.resultUrls.length > 0) {
                generatedImageUrl = apiData.resultUrls[0];
              } else if (apiData.images && apiData.images.length > 0) {
                generatedImageUrl = apiData.images[0];
              } else if (apiData.result && apiData.result.images && apiData.result.images.length > 0) {
                generatedImageUrl = apiData.result.images[0];
              } else if (apiData.resultImageUrl) {
                generatedImageUrl = apiData.resultImageUrl;
              }
              
              // 检查是否有错误
              if (apiData.successFlag === 3) {
                console.error('Task failed:', apiData.errorCode, apiData.errorMessage);
                if (!errorMessage) {
                  errorMessage = apiData.errorMessage || "Unknown error";
                }
              }
            } else {
              // 使用我们的增强格式
              if (data.result?.images && data.result.images.length > 0) {
                generatedImageUrl = data.result.images[0];
              } else if (data.data?.response?.resultUrls && data.data.response.resultUrls.length > 0) {
                generatedImageUrl = data.data.response.resultUrls[0];
              } else if (data.response?.resultUrls && data.response.resultUrls.length > 0) {
                generatedImageUrl = data.response.resultUrls[0];
              }
            }
            
            if (!generatedImageUrl) {
              console.error("Unable to get generated image URL", data);
              
              // 特殊处理POSSIBLE_COMPLETED状态
              if (currentStatus === "POSSIBLE_COMPLETED") {
                resetProcessingState("Task may be completed but we couldn't get the image URL. Please check your history or refresh the page later.");
                toast.info("Task may be completed, but we couldn't get the result. Please check your history later.", {
                  duration: 8000
                });
              } else {
                resetProcessingState("Unable to get generated image URL");
                toast.error("Unable to get generated image");
              }
              return;
            }
            
            console.log("Got generated image URL:", generatedImageUrl);

            // 处理水印逻辑
            try {
              console.log('🎯 Starting watermark processing for generated image...');
              const processedImages = await processImagesWithWatermark([generatedImageUrl], taskId, showWatermark);
              const finalImageUrl = processedImages[0];
              console.log('🎯 Watermark processing completed, final URL:', finalImageUrl.substring(0, 100) + '...');

              // 设置处理后的图片结果
              setResult(prev => ({
                ...prev,
                generatedImageUrl: finalImageUrl
              }));
            } catch (watermarkError) {
              console.error('❌ Watermark processing failed:', watermarkError);
              // 如果水印处理失败，使用原始图片
              setResult(prev => ({
                ...prev,
                generatedImageUrl
              }));
            }
            
            // 将处理后的图像上传到R2
            try {
              const uniqueFilename = `restore-old-photos-${uuidv4()}.png`; // 修改为.png后缀
              //console.log("Preparing to upload processed image to R2, filename:", uniqueFilename);
              // 获取当前显示的图片URL（可能是水印处理后的）
              const currentImageUrl = result.generatedImageUrl || generatedImageUrl;
              const r2Url = await uploadToR2(currentImageUrl, uniqueFilename);
              
              // 如果成功获取到R2 URL，更新结果
              if (r2Url && r2Url !== generatedImageUrl) {
                setResult(prev => ({
                  ...prev,
                  r2ImageUrl: r2Url
                }));
                console.log("Updated R2 image URL:", r2Url);
                
                // 使用R2的URL更新数据库记录
                await updateRecordStatus(taskId, r2Url);
              } else {
                // 如果R2上传失败，使用处理后的URL更新数据库
                const currentImageUrl = result.generatedImageUrl || generatedImageUrl;
                await updateRecordStatus(taskId, currentImageUrl);
              }
            } catch (uploadError) {
              console.error("Failed to upload to R2:", uploadError);
              // 即使上传失败，也继续显示图片给用户，并使用处理后的URL更新数据库
              toast.error("Failed to backup image, but you can still view and download the image");
              const currentImageUrl = result.generatedImageUrl || generatedImageUrl;
              await updateRecordStatus(taskId, currentImageUrl);
            }
            
            setPerceptionProgress(100);
            setProgress(100);
            setIsProcessing(false);
            setStatus('COMPLETED');
            
            // 扣除积分 - 确保只扣除一次
            await deductCredits(taskId);
            
            toast.success("Image generated successfully!");
            //console.log("Task completed, generated image URL:", generatedImageUrl);
          } 
          // 处理可能完成但没有图像URL的情况
          else if (currentStatus === "POSSIBLE_COMPLETED" && minutesSinceLastProgress > 120) {
            clearInterval(interval);
            clearInterval(failureCheckInterval);
            if (pollingTimeout) clearTimeout(pollingTimeout);
            
            console.log("Task may be completed but unable to get image URL");
            
            resetProcessingState("Task may be completed, but we couldn't get the result. Please check your history.");
            toast.info("Task may be completed, but we couldn't get the result. Please check your history later.", {
              duration: 8000
            });
          }
        } catch (error) {
          console.error("Error polling task status:", error);
        }
      }, 3000); // 每3秒轮询一次
      
      // 存储轮询间隔引用，但不在状态中
      const currentPollingInterval = interval;
      setPollingInterval(currentPollingInterval);
      
      // 清理函数
      return () => {
        clearInterval(currentPollingInterval);
        clearInterval(failureCheckInterval);
        if (pollingTimeout) clearTimeout(pollingTimeout);
      };
    }
  }, [taskId, status, isProcessing, error, progress]); // 移除 pollingInterval 依赖

  // 重置处理状态
  const resetProcessingState = (errorMessage?: string) => {
    setIsProcessing(false);
    setStatus('IDLE');
    setPerceptionProgress(0);
    setProgress(0);
    setTaskId(null);

    if (errorMessage) {
      setError(errorMessage);
      toast.error(errorMessage);
    } else {
      setError(null);
    }
  };

  // 处理水印切换
  const handleWatermarkToggle = (checked: boolean) => {
    // 检查用户是否需要强制水印（积分 <= 5）
    if (userNeedsWatermark && !checked) {
      // 积分不足的用户尝试关闭水印，显示升级提示
      toast.error('积分不足，无法移除水印。请升级账户获得更多积分。');
      // 不改变水印状态，保持开启
      return;
    }

    if (!isUserPro && !checked) {
      // Non-paid users trying to disable watermark, show upgrade prompt
      toast.error('请升级账户以移除水印功能。');
      // Don't change watermark state, keep it enabled
      return;
    }
    // Paid users can freely control watermark
    setShowWatermark(checked);
  };

  // 确保图像是有效的 Data URL 格式
  const ensureDataURL = (imageData: string): string => {
    // 如果已经是 Data URL 格式，直接返回
    if (imageData.startsWith('data:image/')) {
      return imageData;
    }
    
    // 如果是 Base64 字符串，转换为 Data URL
    try {
      if (imageData.match(/^[A-Za-z0-9+/=]+$/)) {
        return `data:image/jpeg;base64,${imageData}`;
      }
    } catch (error) {
      console.error('Error converting Base64 string:', error);
    }
    
    // 默认假设为 JPEG
    return imageData;
  };

  // 安全地解析 JSON 响应
  const safeParseJSON = async (response: Response): Promise<any> => {
    try {
      const text = await response.text();
      try {
        return JSON.parse(text);
      } catch (error) {
        const jsonError = error as Error;
        console.error('JSON parsing error, original response:', text);
        throw new Error(`JSON parsing error: ${jsonError.message}`);
      }
    } catch (error) {
      console.error('Error reading response text:', error);
      throw new Error('Unable to read response data');
    }
  };

  // 处理图片上传
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      toast.error('Please upload a valid image file');
      return;
    }

    // 验证文件大小 (最大 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('Image file cannot exceed 10MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setSelectedImage(e.target.result as string);
      }
    };
    reader.readAsDataURL(file);
  };

  // 清除已选图片
  const handleClearImage = () => {
    setSelectedImage(null);
    setResult({});
    resetProcessingState();
  };

  // 上传图像到R2存储
  const uploadToR2 = async (imageUrl: string, filename: string) => {
    // 确保用户已登录
    if (!session) {
      console.error('Attempted to upload image without authentication');
      toast.error('Authentication required. Please sign in first.');
      router.push('/auth/signin');
      return imageUrl; // 返回原始URL，不进行上传
    }

    try {
      console.log(`Starting image upload to R2, source URL: ${imageUrl.substring(0, 50)}...`);
      
      const response = await fetch('/api/upload-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sourceUrl: imageUrl,
          filename: filename,
          taskId: taskId,
          // 添加用户会话信息以便后端验证
          _sessionCheck: {
            userEmail: session.user.email,
            userId: session.user.id
          }
        }),
      });

      console.log("R2 upload response status:", response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("R2 upload failed, response:", errorText);
        throw new Error(`Upload failed: ${errorText}`);
      }
      
      const data = await response.json();
      console.log("R2 upload successful, returned data:", data);
      
      // 保存R2 URL，用于后续下载
      setResult(prevResult => ({
        ...prevResult,
        generatedImageUrl: data.url,
        r2ImageUrl: data.url // 新增字段，专门存储R2的URL
      }));
      
      console.log("R2 upload URL:", data.url);
      return data.url;
    } catch (err: any) {
      console.error("R2 upload failed:", err);
      // 不抛出错误，让用户仍然可以看到原始图片
      return imageUrl;
    }
  };

  // 扣除积分
  const deductCredits = async (currentTaskId: string) => {
    try {
      // 检查是否已经扣除过积分 - 使用更可靠的键值
      const deductionKey = `kontext_credits_deducted_${currentTaskId}`;
      
      // 先检查localStorage
      if (localStorage.getItem(deductionKey) === 'true') {
        console.log('Credits already deducted for task ID:', currentTaskId, 'skipping duplicate deduction');
        return;
      }
      
      // 再检查sessionStorage，双重保险
      if (sessionStorage.getItem(deductionKey) === 'true') {
        //console.log('Credits already deducted (session) for task ID:', currentTaskId, 'skipping duplicate deduction');
        return;
      }

      console.log(`Preparing to deduct credits, amount: ${requiredCredits}, task ID: ${currentTaskId}`);
      
      // 立即标记为已扣除，防止并发请求导致重复扣除
      localStorage.setItem(deductionKey, 'true');
      sessionStorage.setItem(deductionKey, 'true');
      
      const response = await fetch('/api/deduct-credits', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          credits: requiredCredits,
          taskId: currentTaskId, // 添加taskId到请求中，便于后端检查
        }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setUserCredits(data.creditsLeft);
        //console.log(`Credits deducted successfully, remaining credits: ${data.creditsLeft}, task ID: ${currentTaskId}`);
        
        // 显示积分扣除成功通知
        toast.success(`Photo restored successfully.`);
      } else {
        //console.error("Failed to deduct credits:", data.error);
        
        if (data.error === "insufficient_credits") {
          toast.error("Insufficient credits, please recharge");
        } else if (data.error === "already_deducted") {
          console.log("Credits were already deducted for this task");
        } else {
          toast.error("Failed to deduct credits, but photo has been restored");
        }
      }
    } catch (error) {
      console.error("Error deducting credits:", error);
      toast.error("Error occurred during credit deduction, please contact support");
    }
  };

  // 生成图像
  const handleGenerate = async () => {
    // 图片检查
    if (!selectedImage) {
      toast.error('Please upload an image');
      return;
    }

    // 严格的会话验证 - 确保用户已登录
    if (!session || !session.user) {
      toast.error('Authentication required. Please sign in first.');
      router.push('/auth/signin');
      return;
    }

    // 确保用户有必要的身份信息
    if (!session.user.id && !session.user.email) {
      toast.error('Invalid session. Please sign out and sign in again.');
      router.push('/auth/signin');
      return;
    }

    try {
      // 重置状态
      setIsProcessing(true);
      setError(null);
      setTaskId(null);
      setStatus('UPLOADING');
      setResult({});
      setPerceptionProgress(0);
      setProgress(0);
      
      // 生成唯一文件名
      const originalFilename = `damaged-photo-${uuidv4()}.png`;
      
      // 使用JSON格式上传图像
      const uploadResponse = await fetch('/api/upload-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sourceUrl: ensureDataURL(selectedImage),
          filename: originalFilename,
          // 添加用户会话信息以便后端验证
          _sessionCheck: {
            userEmail: session.user.email,
            userId: session.user.id
          }
        }),
      });
      
      if (!uploadResponse.ok) {
        // 检查是否是认证错误
        if (uploadResponse.status === 401) {
          toast.error('Authentication required. Please sign in again.');
          router.push('/auth/signin');
          resetProcessingState();
          return;
        }
        
        const errorData = await safeParseJSON(uploadResponse).catch(() => ({ error: 'Failed to parse upload response' }));
        console.error('Failed to upload image:', errorData);
        throw new Error(`Failed to upload image: ${errorData.error || uploadResponse.statusText}`);
      }
      
      const uploadData = await safeParseJSON(uploadResponse).catch(err => {
        console.error('Failed to parse upload response:', err);
        throw new Error(`Failed to parse upload response: ${err.message}`);
      });
      
      const originalImageUrl = uploadData.url;
      
      // 更新状态
      setResult({ originalImageUrl });
      setStatus('GENERATING');
      setPerceptionProgress(5);
      
      // 增加提示消息
      toast.info("Connecting to AI service...", { duration: 3000 });
      
      // 组合用户输入的提示词和默认提示词
      const defaultPrompt = "This is a severely damaged old photo. Restore and colorize this image. Remove any damages or scratches or imperfections. Make it vivid. Pay attention to the details of the characters and clothing. Repair the damaged parts of the photo, modify the damaged parts of the clothes, and repair other damaged parts of the photo.";
      const finalPrompt = prompt.trim() ? `${prompt.trim()}, ${defaultPrompt}` : defaultPrompt;
      
      // 调用Kontext API
      console.log('session status:', !!session, 'user ID:', session.user.id);
      const response = await fetch('/api/kontext/generate', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          prompt: finalPrompt,
          imageUrl: originalImageUrl,
          aspectRatio: aspectRatio || '1:1',
          // 添加用户会话信息以便调试
          _debug: {
            userEmail: session.user.email,
            userId: session.user.id,
            authStatus: 'authenticated'
          }
        }),
      });

      const responseText = await response.text();
      console.log('API response raw text:', responseText);
      
      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch (jsonError) {
        console.error('Failed to parse API response JSON:', jsonError, 'Original response:', responseText);
        throw new Error(`Failed to parse API response: ${(jsonError as Error).message}`);
      }
      
      if (!response.ok) {
        // 记录更详细的错误信息
        console.error('API response error, status code:', response.status);
        console.error('Response headers:', Object.fromEntries(response.headers.entries()));
        console.error('Response data:', responseData);
        
        if (responseData.error === "insufficient_credits") {
          toast.error("Insufficient credits, please recharge");
          router.push("/pricing");
          resetProcessingState();
          return;
        }
        throw new Error(responseData.message || 'Failed to restore photo');
      }

      // 获取结果
      console.log('Response successful, data:', responseData);
      
      // 提取任务ID (可能在不同的位置)
      let extractedTaskId = responseData.taskId;
      if (!extractedTaskId && responseData.data && responseData.data.taskId) {
        extractedTaskId = responseData.data.taskId;
      }
      
      if (!extractedTaskId) {
        console.error('Unable to extract task ID from response:', responseData);
        throw new Error('Task ID not returned, please try again later');
      }
      
      setTaskId(extractedTaskId);
      console.log('Setting task ID:', extractedTaskId);
      
      // 存储所需积分以便后续扣除
      if (responseData.requiredCredits) {
        setRequiredCredits(responseData.requiredCredits);
      }
      
      // 更新状态信息
      toast.success("Photo restoration task submitted successfully! Processing may take a few minutes.");
      setPerceptionProgress(10); // 立即给用户一些视觉反馈
      
      // 如果API直接返回了图像URL，立即更新结果
      if (responseData.imageUrl) {
        const generatedImageUrl = responseData.imageUrl;
        
        // 先设置结果，以便用户尽快看到
        setResult({
          originalImageUrl,
          generatedImageUrl
        });
        
        // 将生成的图像上传到R2
        try {
          const uniqueFilename = `restore-old-photos-${uuidv4()}.png`; // 修改为.png后缀
          //console.log("Preparing to upload directly returned image to R2, filename:", uniqueFilename);
          const r2Url = await uploadToR2(generatedImageUrl, uniqueFilename);
          
          // 使用R2的URL更新数据库记录
          if (r2Url && r2Url !== generatedImageUrl) {
            //console.log("Updated R2 image URL:", r2Url);
            await updateRecordStatus(extractedTaskId, r2Url);
          } else {
            // 如果R2上传失败，使用原始URL更新数据库
            await updateRecordStatus(extractedTaskId, generatedImageUrl);
          }
        } catch (uploadError) {
          //console.error("Failed to upload to R2:", uploadError);
          // 即使上传失败，也使用原始URL更新数据库
          await updateRecordStatus(extractedTaskId, generatedImageUrl);
        }
        
        setStatus('COMPLETED');
        setPerceptionProgress(100);
        setProgress(100);
        setIsProcessing(false);
        await deductCredits(extractedTaskId);
        
        toast.success("Photo restored successfully!");
        //console.log("Task completed, generated image URL:", generatedImageUrl);
      } else {
        // 如果没有立即返回结果，提示用户任务正在后台处理
        toast.info("Task submitted, image is being generated, please wait...", {
          duration: 5000
        });
      }
    } catch (error) {
      //console.error('Error generating image:', error);
      
      // 更详细地记录错误信息
      let errorMessage = 'Failed to restore photo';
      
      if (error instanceof Error) {
        errorMessage = error.message;
        
        // 检查是否为JSON解析错误
        if (error.message.includes('JSON')) {
          console.error('JSON parsing error, possibly API response format issue:', error);
          errorMessage = 'API response format error, please try again later';
        }
        
        // 检查是否为网络错误
        if (error.message.includes('network') || error.message.includes('fetch')) {
          console.error('Network error:', error);
          errorMessage = 'Network connection error, please check your internet connection';
        }
        
        // 检查是否为任务ID相关错误
        if (error.message.includes('Task ID not returned')) {
          console.error('Task ID error:', error);
          errorMessage = 'Task submitted but ID not returned, please check results later';
          
          // 尝试继续处理，不立即停止
          toast.info("Task may be processing, please refresh the page later to check results", {
            duration: 8000
          });
          
          // 延迟重置处理状态，给用户一些时间看到消息
          setTimeout(() => {
            resetProcessingState();
          }, 5000);
          return;
        }
      } else {
        console.error('Unknown error type:', typeof error, error);
      }
      
      resetProcessingState(`Image generation failed: ${errorMessage}`);
    }
  };

  // 在组件加载时检查是否有未完成的任务，但不自动恢复
  useEffect(() => {
    if (mounted && session) {
      // 检查localStorage中是否有保存的任务ID
      const savedTaskId = localStorage.getItem('kontext_last_task_id');
      const savedTimestamp = localStorage.getItem('kontext_last_task_timestamp');
      
      if (savedTaskId && savedTimestamp) {
        const timestamp = parseInt(savedTimestamp, 10);
        const now = Date.now();
        const hoursPassed = (now - timestamp) / (1000 * 60 * 60);
        
        // 如果任务在24小时内创建，询问用户是否恢复任务
        if (hoursPassed < 24) {
          console.log('Found saved task ID:', savedTaskId, 'created at', new Date(timestamp).toLocaleString());
          
          // 删除任务记录，防止下次自动恢复
          localStorage.removeItem('kontext_last_task_id');
          localStorage.removeItem('kontext_last_task_timestamp');
          
          // 向用户显示通知，询问是否要恢复任务
          toast.info(
            "We detected an unfinished image generation task. You can ignore this message or start over.",
            { duration: 5000 }
          );
        } else {
          // 清除过期的任务信息
          localStorage.removeItem('kontext_last_task_id');
          localStorage.removeItem('kontext_last_task_timestamp');
        }
      }
    }
  }, [mounted, session]);

  // 当任务完成或出错时清除localStorage
  useEffect(() => {
    if (status === 'COMPLETED' || error) {
      localStorage.removeItem('kontext_last_task_id');
      localStorage.removeItem('kontext_last_task_timestamp');
    }
  }, [status, error]);

  // 更新数据库记录状态
  const updateRecordStatus = async (taskId: string, imageUrl: string) => {
    try {
      //console.log(`Preparing to update task record status, task ID: ${taskId}`);
      
      // 调用API更新记录状态
      const response = await fetch(`/api/kontext/update-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskId,
          status: 'COMPLETED',
          generatedImageUrl: imageUrl
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Failed to parse response' }));
        console.error('Failed to update record status:', errorData);
        return false;
      }
      
      const data = await response.json();
      console.log('Record status updated successfully:', data);
      return true;
    } catch (error) {
      console.error('Error updating record status:', error);
      return false;
    }
  };

  return (
    <section className="photo-restore-section py-8">
      <div className="container px-4 mx-auto">
        <Card className="w-full max-w-3xl mx-auto p-6 mb-8 bg-white/90 backdrop-blur-sm shadow-lg border border-blue-100">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Restore Old Photos with AI</h2>
              <div className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full flex items-center gap-2">
                <span className="text-sm font-normal">Credits:</span>{" "}
                {mounted && session ? (
                  <span className="text-lg font-bold">{userCredits !== null ? userCredits : "Loading..."}</span>
                ) : (
                  <span className="text-lg font-bold">
                    <a href="/auth/signin" className="hover:underline">Sign in</a>
                  </span>
                )}
                <svg
                  className="w-4 h-4 text-amber-500 fill-current"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0-15c-4.97 0-9 4.03-9 9v7c0 1.66 1.34 3 3 3h12c1.66 0 3-1.34 3-3v-7c0-4.97-4.03-9-9-9zm0 3c3.31 0 6 2.69 6 6v7c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1v-7c0-3.31 2.69-6 6-6z" />
                </svg>
              </div>
            </div>

            {/* Login navigation for non-authenticated users */}
            {mounted && !session && (
              <div className="text-center">
                <a 
                  href="/auth/signin" 
                  className="text-blue-600 hover:text-blue-800 underline text-sm"
                >
                  <strong>Sign in for free trial</strong>
                </a>
              </div>
            )}
            
            <p className="text-sm text-muted-foreground text-center">Step 1: Upload your damaged photo (scratches, fading, etc.)</p>
            <p className="text-sm text-muted-foreground text-center">Step 2: AI restoration brings your memories back to life</p>
            
            {/* Prompt input */}
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium text-muted-foreground">
                Prompt (Optional - we'll use our default restoration prompt if empty)
              </label>
              <Textarea
                placeholder="Add your specific instructions here if needed"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="min-h-[80px] bg-white/80"
              />
              <p className="text-xs text-muted-foreground">
                Our default prompt will restore and colorize damaged photos automatically
              </p>
            </div>
            
            {/* Image upload area */}
            <div className="relative min-h-[300px] rounded-lg border-2 border-dashed border-blue-200 hover:border-blue-300 transition-colors bg-blue-50/30">
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                aria-label="Upload image"
              />
              <div className="absolute inset-0 flex flex-col items-center justify-center gap-2">
                {selectedImage ? (
                  <div className="relative w-full h-full">
                    <Image
                      src={selectedImage}
                      alt="Uploaded image"
                      className="object-contain"
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      priority
                    />
                    <Button
                      variant="secondary"
                      size="icon"
                      className="absolute top-2 right-2 bg-background/80 backdrop-blur-sm hover:bg-background/90 z-20"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleClearImage();
                      }}
                    >
                      <X className="h-4 w-4" />
                      <span className="sr-only">Clear image</span>
                    </Button>
                  </div>
                ) : (
                  <div className="flex flex-col items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors">
                    <Upload className="w-8 h-8" />
                    <p className="text-sm font-medium">Upload image</p>
                    <p className="text-xs text-muted-foreground/70">Click or drag and drop</p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Aspect ratio selection */}
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium text-muted-foreground">
                Select aspect ratio
              </label>
              <Select
                value={aspectRatio}
                onValueChange={setAspectRatio}
              >
                <SelectTrigger className="w-full bg-white/80">
                  <SelectValue placeholder="Select aspect ratio" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1:1">Square (1:1)</SelectItem>
                  <SelectItem value="16:9">Widescreen (16:9)</SelectItem>
                  <SelectItem value="9:16">Mobile (9:16)</SelectItem>
                  <SelectItem value="4:3">Standard (4:3)</SelectItem>
                  <SelectItem value="3:4">Portrait (3:4)</SelectItem>
                  <SelectItem value="21:9">Ultrawide (21:9)</SelectItem>
                  <SelectItem value="16:21">Tall Portrait (16:21)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Watermark toggle */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label htmlFor="watermark-toggle" className="text-sm font-medium text-muted-foreground">
                  Add Watermark
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-[200px]">
                      <p>
                        {userNeedsWatermark
                          ? `Upgrade to remove watermarks.`
                          : `Upgrade to remove watermarks from your generated images`
                        }
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  id="watermark-toggle"
                  checked={showWatermark}
                  onCheckedChange={handleWatermarkToggle}
                  disabled={isProcessing || userNeedsWatermark}
                />
                {userNeedsWatermark && (
                  <Button
                    size="sm"
                    className="text-xs px-3 py-1 h-7 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 rounded-full"
                    onClick={() => window.open('/pricing', '_blank')}
                  >
                    Upgrade
                  </Button>
                )}
              </div>
            </div>

            {/* Generate button */}
            <Button 
              className="w-full bg-gradient-to-r from-blue-600 to-blue-800 hover:from-blue-700 hover:to-blue-900 text-white"
              size="lg"
              onClick={handleGenerate}
              disabled={!selectedImage || isProcessing}
            >
              <span className="flex items-center gap-2">
                {isProcessing ? (
                  <>
                    <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                        fill="none"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                    {status === "GENERATING" ? (
                      <>
                        Generating... <span className="numeric-content">{Math.round(perceptionProgress)}%</span>
                      </>
                    ) : "Processing..."}
                  </>
                ) : (
                  <>
                    <svg
                      className="w-5 h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 6V18M18 12L6 12"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    Restore This Photo
                  </>
                )}
              </span>
            </Button>
            
            {/* Progress bar */}
            {status === "GENERATING" && (
              <div className="w-full bg-blue-100 rounded-full h-2.5">
                <div 
                  className="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
                  style={{ width: `${perceptionProgress}%` }}
                ></div>
              </div>
            )}

            {/* Processing message */}
            {status === "GENERATING" && (
              <div className="p-4 rounded-lg bg-blue-50 border border-blue-200">
                <p className="text-sm text-blue-800">
                  Your image is being generated. This typically takes 1-2 minutes. Please don&apos;t close this page.
                </p>
              </div>
            )}

            {/* Error display */}
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm">
                {error}
              </div>
            )}

            {/* Results display */}
            {result.generatedImageUrl && (
              <div className="space-y-4">
                <div className="relative min-h-[300px] rounded-lg border border-blue-200 bg-white/80">
                  <Image
                    src={result.generatedImageUrl}
                    alt="Generated image"
                    className="object-contain"
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    priority
                  />
                </div>
                <Button 
                  className="w-full bg-green-600 hover:bg-green-700 text-white"
                  size="lg"
                  onClick={() => {
                    // Prefer R2 URL
                    const imageUrl = result.r2ImageUrl || result.generatedImageUrl;
                    if (imageUrl) {
                      window.open(imageUrl, '_blank');
                    }
                  }}
                >
                  <span className="flex items-center gap-2">
                    <svg 
                      className="w-5 h-5" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24" 
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                      />
                    </svg>
                    View Full Image
                  </span>
                </Button>
                
                <Button 
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  size="lg"
                  onClick={async () => {
                    try {
                      console.log("Starting image download...");
                      // Prefer R2 URL, then generated URL
                      const urlToDownload = result.r2ImageUrl || result.generatedImageUrl;
                      console.log("Original download URL:", urlToDownload?.substring(0, 100) + "...");
                      
                      if (!urlToDownload) {
                        console.error("No image URL available");
                        toast.error('No image URL available');
                        return;
                      }
                      
                      // Use image proxy API to download, avoiding CORS issues
                      console.log("Preparing to download image via proxy...");
                      toast.info("Preparing download...", { duration: 2000 });
                      
                      // Method 1: Use our proxy API for download
                      const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(urlToDownload)}`;
                      console.log("Proxy URL:", proxyUrl);
                      
                      // Create a hidden anchor tag to trigger download
                      const link = document.createElement('a');
                      link.href = proxyUrl;
                      link.download = 'restore-old-photos.png';
                      link.target = '_blank'; // Open in new tab to avoid navigation issues
                      console.log("Ready to trigger download...");
                      document.body.appendChild(link);
                      link.click();
                      
                      // Delay removing link
                      setTimeout(() => {
                        document.body.removeChild(link);
                        console.log("Download link removed");
                      }, 1000);
                      
                      console.log("Download process completed");
                      toast.success("Image download started");
                    } catch (error) {
                      console.error('Failed to download image:', error);
                      toast.error(`Download failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                      
                      // Provide fallback download method
                      console.log("Trying fallback download method...");
                      const urlToDownload = result.r2ImageUrl || result.generatedImageUrl;
                      if (urlToDownload) {
                        // Open image in new window, user can right-click to save
                        window.open(urlToDownload, '_blank');
                        toast.info("Please right-click on the image in the new window and select 'Save Image As...' to download");
                      }
                    }
                  }}
                >
                  <span className="flex items-center gap-2">
                    <svg 
                      className="w-5 h-5" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24" 
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                      />
                    </svg>
                    Download Image
                  </span>
                </Button>
              </div>
            )}
          </div>
        </Card>
      </div>
    </section>
  );
};

export default Kontext; 