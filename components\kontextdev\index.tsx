"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { v4 as uuidv4 } from 'uuid';
import { toast } from "sonner";
import { processImagesWithWatermark } from '@/utils/watermark';
import Image from "next/image";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Upload, X, HelpCircle } from "lucide-react";
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  Too<PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { UserCredits } from "@/types/user";

// 定义上色风格的数据结构
interface ColorStyle {
  id: string;
  name: string;
  prompt: string;
}

interface ColorStyleCategory {
  category: string;
  styles: ColorStyle[];
}

// 上色风格数据
const colorStyleCategories: ColorStyleCategory[] = [
  {
    category: "Function/Purpose-Oriented Styles (Practical)",
    styles: [
      {
        id: "realistic-restoration",
        name: "Realistic Restoration",
        prompt: "Colorize this black and white photo with natural, true-to-life colors. Reproduce accurate skin tones, clothing fabrics, and environmental details as they would appear in real life."
      },
      {
        id: "vintage-retro",
        name: "Vintage/Retro",
        prompt: "Add vintage-inspired colors to this black and white photo. Use slightly faded tones, warm sepia influence, and subtle grain to mimic old film photography."
      },
      {
        id: "modern-enhancement",
        name: "Modern Enhancement",
        prompt: "Enhance this black and white photo into a modern color version. Apply vivid, clean, and sharp colors with high clarity and digital-like quality."
      }
    ]
  },
  {
    category: "Mood/Emotion-Oriented Styles (Atmospheric)",
    styles: [
      {
        id: "warm-nostalgia",
        name: "Warm Nostalgia",
        prompt: "Colorize this black and white photo with warm golden and brown tones. Create a cozy, sentimental feeling, like a cherished memory."
      },
      {
        id: "dreamy-romantic",
        name: "Dreamy Romantic",
        prompt: "Add soft pastel pinks, lavenders, and glowing light effects to this photo. Create a dreamy, romantic atmosphere with gentle highlights."
      },
      {
        id: "cold-epic",
        name: "Cold Epic",
        prompt: "Apply desaturated, cool blue and gray tones to this black and white image. Make it look cinematic, dramatic, and historically epic."
      },
      {
        id: "natural-freshness",
        name: "Natural Freshness",
        prompt: "Use fresh greens, clear blues, and natural sunlight tones to colorize this photo. Emphasize outdoor beauty and organic scenery."
      },
      {
        id: "retro-aesthetic",
        name: "Retro Aesthetic",
        prompt: "Colorize this black and white photo with vintage-inspired palettes: muted reds, faded yellows, and rustic browns. Simulate old analog film tones."
      },
      {
        id: "melancholic-muted",
        name: "Melancholic/Muted",
        prompt: "Add subdued, muted colors with soft grays and blues. Create a calm, melancholic mood with gentle contrast."
      }
    ]
  },
  {
    category: "Technical/Stylistic Styles (Professional)",
    styles: [
      {
        id: "high-saturation",
        name: "High Saturation",
        prompt: "Apply bold, vivid colors with strong contrast. Make the photo eye-catching and full of energy."
      },
      {
        id: "low-saturation-matte",
        name: "Low Saturation/Matte",
        prompt: "Use muted, desaturated tones with a matte finish. Keep the image subtle and understated."
      },
      {
        id: "hdr",
        name: "HDR",
        prompt: "Colorize with high dynamic range—deep shadows, bright highlights, and detailed textures."
      },
      {
        id: "impressionistic",
        name: "Impressionistic",
        prompt: "Colorize in an impressionist painting style with vibrant brushstroke-like colors."
      },
      {
        id: "hand-tinted",
        name: "Hand-Tinted",
        prompt: "Simulate early 20th-century hand-colored photography with uneven but charming tones."
      },
      {
        id: "anime-illustration",
        name: "Anime/Illustration",
        prompt: "Apply flat, bold colors with sharp outlines. Style it like an anime or graphic illustration."
      },
      {
        id: "surreal-fantasy",
        name: "Surreal/Fantasy",
        prompt: "Use imaginative, unreal colors—purple skies, neon lights, glowing elements—for a fantasy effect."
      },
      {
        id: "warm-cold-split",
        name: "Warm/Cold Split",
        prompt: "Colorize with cinematic grading—warm highlights (orange) and cool shadows (teal)."
      },
      {
        id: "film-grain",
        name: "Film Grain",
        prompt: "Add realistic colors with grainy texture, like analog 35mm film."
      },
      {
        id: "vintage-filters",
        name: "Vintage Filters",
        prompt: "Apply faded, washed-out tones with sepia influence for an aged retro effect."
      }
    ]
  },
  {
    category: "Subject-Oriented Styles (What Is Colored)",
    styles: [
      {
        id: "portrait-focused",
        name: "Portrait Focused",
        prompt: "Colorize this portrait with realistic skin tones, natural hair shades, and subtle fabric details."
      },
      {
        id: "landscape-nature-focused",
        name: "Landscape/Nature Focused",
        prompt: "Add natural greens, sky blues, and earthy browns. Highlight seasonal colors for a lively landscape."
      },
      {
        id: "architecture-urban-focused",
        name: "Architecture/Urban Focused",
        prompt: "Apply realistic urban tones—brick reds, concrete grays, warm lights—bringing old streets to life."
      },
      {
        id: "selective-colorization",
        name: "Selective Colorization",
        prompt: "Keep most of the photo black and white. Only colorize the subject with vivid tones for striking contrast."
      }
    ]
  },
  {
    category: "Tone/Palette-Oriented Styles (Color Harmony)",
    styles: [
      {
        id: "pastel-palette",
        name: "Pastel Palette",
        prompt: "Use soft pastel colors—mint, baby blue, light pink, lavender—for a delicate, airy mood."
      },
      {
        id: "earthy-rustic-palette",
        name: "Earthy/Rustic Palette",
        prompt: "Apply earthy browns, greens, and beige tones for a grounded, rustic feeling."
      },
      {
        id: "bold-pop-palette",
        name: "Bold/Pop Palette",
        prompt: "Colorize with bright, saturated colors like red, yellow, and turquoise. Make the image vibrant and modern."
      },
      {
        id: "duotone-bicolor-palette",
        name: "Duotone/Bicolor Palette",
        prompt: "Transform the photo with a bold duotone palette—blue and orange only—for graphic impact."
      },
      {
        id: "spring-blossom",
        name: "Spring Blossom",
        prompt: "Use light greens and blooming pinks to create a fresh spring atmosphere."
      },
      {
        id: "autumn-aura",
        name: "Autumn Aura",
        prompt: "Add warm oranges, yellows, and browns for a cozy autumn feeling."
      },
      {
        id: "winter-chill",
        name: "Winter Chill",
        prompt: "Apply icy blues, whites, and cold grays for a frosty winter look."
      },
      {
        id: "summer-glow",
        name: "Summer Glow",
        prompt: "Use bright yellows, turquoise, and golden tones for a sunny summer vibe."
      }
    ]
  },
  {
    category: "Special/Experimental Styles",
    styles: [
      {
        id: "hybrid-styles",
        name: "Hybrid Styles",
        prompt: "Combine realistic skin tones with creative, surreal backgrounds. Mix natural and artistic coloring styles."
      },
      {
        id: "ai-generative-styles",
        name: "AI/Generative Styles",
        prompt: "Allow AI to reinterpret this photo with unexpected, abstract, and artistic color choices."
      },
      {
        id: "minimalist-monochrome-tint",
        name: "Minimalist/Monochrome Tint",
        prompt: "Apply a single-color tint across the whole photo (cyanotype blue, sepia brown, or red wash)."
      },
      {
        id: "cross-media-styles",
        name: "Cross-Media Styles",
        prompt: "Colorize in a non-photographic style—comic book shading, watercolor painting, or neon cyberpunk tones."
      }
    ]
  }
];

// 添加组件配置参数接口
interface KontextDevProps {
  title?: string;
  showPromptInput?: boolean;
  showTranslation?: boolean;
  showQuickActions?: boolean;
  showImageSettings?: boolean;
  showWatermarkToggle?: boolean;
  showColorizationStyles?: boolean; // 控制是否显示上色风格选择器
  defaultPrompt?: string;
  generateButtonText?: string;
  resultTitle?: string;
  disableTranslation?: boolean; // 默认为true，禁用翻译
}

export default function KontextDev({
  title = "Restore Old Photos",
  showPromptInput = true,
  showTranslation = false, // 默认不显示翻译选项
  showQuickActions = true,
  showImageSettings = true,
  showWatermarkToggle = true,
  showColorizationStyles = false, // 默认不显示上色风格选择器
  defaultPrompt = "",
  generateButtonText = "Restore Old Photos",
  resultTitle = "Restore Old Photos Result",
  disableTranslation = true // 默认禁用翻译
}: KontextDevProps = {}) {
  // Session and mounting state
  const { data: session } = useSession();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [userCredits, setUserCredits] = useState<number | null>(null);

  // Basic states
  const [translate, setTranslate] = useState(true); // 默认启用翻译
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imageUrl, setImageUrl] = useState<string>("");
  const [prompt, setPrompt] = useState<string>(defaultPrompt);
  const [translatedPrompt, setTranslatedPrompt] = useState<string>("");
  
  // Processing states
  const [isTranslating, setIsTranslating] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [perceptionProgress, setPerceptionProgress] = useState(0);
  const [progress, setProgress] = useState(0);
  
  // Image settings
  const [aspectRatio, setAspectRatio] = useState<string>("match_input_image");
  const [showMoreAspectRatios, setShowMoreAspectRatios] = useState(false);
  const [outputFormat, setOutputFormat] = useState<string>("png");
  const [imageCount, setImageCount] = useState<number>(1);

  // Colorization style settings
  const [selectedStyle, setSelectedStyle] = useState<string>(showColorizationStyles ? "realistic-restoration" : "");
  const [showStyleSelector, setShowStyleSelector] = useState<boolean>(false);

  // Watermark settings
  const [showWatermark, setShowWatermark] = useState(true);
  const [isUserPro, setIsUserPro] = useState(false);
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [showComingSoonDialog, setShowComingSoonDialog] = useState(false);
  const [userNeedsWatermark, setUserNeedsWatermark] = useState(true); // 用户是否需要强制水印

  // 添加一个状态来跟踪提示词是否来自Quick Actions
  const [isPromptFromQuickAction, setIsPromptFromQuickAction] = useState<boolean>(false);

  // 初始化默认风格的提示词
  useEffect(() => {
    if (showColorizationStyles && showPromptInput && selectedStyle === "realistic-restoration" && !prompt) {
      const defaultStyleData = colorStyleCategories
        .flatMap(category => category.styles)
        .find(style => style.id === "realistic-restoration");

      if (defaultStyleData) {
        setPrompt(defaultStyleData.prompt);
      }
    }
  }, [showColorizationStyles, showPromptInput, selectedStyle, prompt]);

  // 在组件的状态部分添加新的状态变量
  const [originalImageUrls, setOriginalImageUrls] = useState<string[]>([]);

  // 使用默认提示词
  useEffect(() => {
    if (defaultPrompt) {
      setPrompt(defaultPrompt);
      // 如果有默认提示词，标记为非Quick Action来源
      setIsPromptFromQuickAction(false);
    }
  }, [defaultPrompt]);

  // 确保translate始终为true
  useEffect(() => {
    setTranslate(true);
  }, []);

  // Check if user is a paid user
  useEffect(() => {
    async function checkUserStatus() {
      try {
        const response = await fetch('/api/credits');
        if (response.ok) {
          const userCredits: UserCredits = await response.json();
          setIsUserPro(userCredits.is_pro === true);
          // If user is a paid user, don't show watermark by default
          if (userCredits.is_pro === true) {
            setShowWatermark(false);
          }
          // Set user credits
          setUserCredits(userCredits.left_credits);
        }
      } catch (error) {
        console.error('Failed to check user status:', error);
      }
    }
    
    setMounted(true);
    checkUserStatus();
  }, []);
  
  // Check session status when component loads
  useEffect(() => {
    if (session) {
      fetchUserCredits();
    }
  }, [session]);

  // 检查用户积分并设置水印状态
  useEffect(() => {
    if (userCredits !== null) {
      const needsWatermark = userCredits <= 5;
      setUserNeedsWatermark(needsWatermark);

      // 如果用户积分不足，强制开启水印
      if (needsWatermark) {
        setShowWatermark(true);
        setIsUserPro(false);
        console.log(`🎨 User credits: ${userCredits} ≤ 5, watermark forced ON`);
      } else {
        // 积分充足的用户，默认关闭水印，但可以控制
        setShowWatermark(false);
        setIsUserPro(true);
        console.log(`✅ User credits: ${userCredits} > 5, watermark OFF by default (user can control)`);
      }
    } else {
      // 如果积分为null（未登录或获取失败），默认需要水印
      setUserNeedsWatermark(true);
      setShowWatermark(true);
      setIsUserPro(false);
      console.log('⚠️ User credits unknown, watermark forced ON');
    }
  }, [userCredits]);

  // 确保未登录用户也有正确的水印状态
  useEffect(() => {
    if (!session) {
      setUserNeedsWatermark(true);
      setShowWatermark(true);
      setIsUserPro(false);
      console.log('🔒 User not logged in, watermark forced ON');
    }
  }, [session]);

  // Fetch user credits
  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/credits');
      if (!response.ok) {
        throw new Error('Failed to fetch user credits');
      }
      const data = await response.json();
      console.log('💰 Fetched user credits:', data.left_credits, 'is_pro:', data.is_pro);
      setUserCredits(data.left_credits);
      setIsUserPro(data.is_pro === true);

      // 不在这里设置水印状态，让useEffect处理
      // 基于积分的逻辑会在useEffect中处理
    } catch (error) {
      console.error('❌ Error fetching user credits:', error);
      // 获取积分失败时，为安全起见，强制开启水印
      setUserCredits(0);
      setUserNeedsWatermark(true);
      setShowWatermark(true);
      setIsUserPro(false);
    }
  };
  
  // Refresh credits after successful generation
  useEffect(() => {
    if (generatedImages.length > 0 && session) {
      fetchUserCredits();
    }
  }, [generatedImages, session]);

  // 添加新的状态来跟踪实际处理开始时间
  const [actualProcessingStarted, setActualProcessingStarted] = useState(false);
  const [generationStartTime, setGenerationStartTime] = useState<number | null>(null);

  // Sync perception progress when real progress updates (only after actual processing starts)
  useEffect(() => {
    if (actualProcessingStarted && progress > perceptionProgress) {
      setPerceptionProgress(progress);
    }
  }, [progress, perceptionProgress, actualProcessingStarted]);

  // Enhanced perception progress simulation with sqrt-based and segmented linear growth
  useEffect(() => {
    if (isGenerating) {
      const startTime = Date.now();
      setGenerationStartTime(startTime);

      const simulatedInterval = setInterval(() => {
        setPerceptionProgress(prev => {
          const elapsed = Date.now() - startTime;
          const waitingDuration = 40000; // 40秒等待时间

          let targetProgress;
          let increment;

          if (elapsed < waitingDuration) {
            // 🚀 准备阶段 (0% → 60%, 40秒) - 使用平方根函数实现非线性增长
            const progressRatio = elapsed / waitingDuration; // 0 到 1
            const sqrtProgress = Math.sqrt(progressRatio); // 平方根函数，前期快后期慢
            targetProgress = Math.min(sqrtProgress * 60, 60); // 映射到 0-60%

            // 动态计算增量，确保平滑过渡
            const timeDelta = 200; // 200ms更新间隔
            const nextProgressRatio = Math.min(1, (elapsed + timeDelta) / waitingDuration);
            const nextSqrtProgress = Math.sqrt(nextProgressRatio);
            const nextTargetProgress = Math.min(nextSqrtProgress * 60, 60);
            increment = Math.max(0.02, (nextTargetProgress - targetProgress) * 5); // 最小增量0.02%
          } else {
            // 🧠 生成阶段 (60% → 100%) - 使用分段线性增长策略
            if (!actualProcessingStarted) {
              setActualProcessingStarted(true);
            }

            // 分段线性增长策略
            if (prev < 65) {
              // 60-65%: 1.2%/秒 (快速，继续正反馈)
              increment = 1.2 * (200 / 1000); // 200ms间隔转换为秒
            } else if (prev < 70) {
              // 65-70%: 0.8%/秒 (中等速度)
              increment = 0.8 * (200 / 1000);
            } else if (prev < 80) {
              // 70-80%: 0.5%/秒 (开始放慢)
              increment = 0.5 * (200 / 1000);
            } else if (prev < 85) {
              // 80-85%: 0.3%/秒 (更慢)
              increment = 0.3 * (200 / 1000);
            } else if (prev < 90) {
              // 85-90%: 0.2%/秒 (很慢)
              increment = 0.2 * (200 / 1000);
            } else if (prev < 95) {
              // 90-95%: 0.1%/秒 (非常慢)
              increment = 0.1 * (200 / 1000);
            } else {
              // 95%+: 0.05%/秒 (极慢，等待真实完成)
              increment = 0.05 * (200 / 1000);
            }

            // 如果有真实进度且超过当前感知进度，使用真实进度
            if (progress > prev) {
              targetProgress = Math.max(prev, progress);
              increment = Math.max(increment, 0.5); // 真实进度时可以更快
            } else {
              targetProgress = Math.min(prev + increment, 100);
            }
          }

          // 计算新的进度值
          const newProgress = Math.min(prev + increment, targetProgress);

          // 确保进度只增不减，且不超过100%
          return Math.min(Math.max(prev, newProgress), 100);
        });
      }, 200); // 200ms更新间隔确保视觉流畅

      return () => clearInterval(simulatedInterval);
    } else {
      // 重置状态
      setActualProcessingStarted(false);
      setGenerationStartTime(null);
    }
  }, [isGenerating, progress, actualProcessingStarted]);

  // Handle watermark toggle change
  const handleWatermarkToggle = (checked: boolean) => {
    // 检查用户是否需要强制水印（积分 <= 5）
    if (userNeedsWatermark && !checked) {
      // 积分不足的用户尝试关闭水印，显示升级提示
      toast.error('积分不足，无法移除水印。请升级账户获得更多积分。');
      setShowUpgradeDialog(true);
      // 不改变水印状态，保持开启
      return;
    }

    if (!isUserPro && !checked) {
      // Non-paid users trying to disable watermark, show upgrade prompt
      setShowUpgradeDialog(true);
      // Don't change watermark state, keep it enabled
      return;
    }
    // Paid users can freely control watermark
    setShowWatermark(checked);
  };
  
  // Handle image count selection
  const handleImageCountChange = (count: number) => {
    if (count > 1) {
      if (isUserPro) {
        // Paid users see feature in development message
        setShowComingSoonDialog(true);
      } else {
        // Non-paid users see upgrade prompt
        setShowUpgradeDialog(true);
      }
      // Keep selection at 1 image
      return;
    }
    
    setImageCount(count);
  };

  useEffect(() => {
    // Ensure Match Input is selected by default
    setAspectRatio("match_input_image");
  }, []);

  // Handle image upload
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please upload a valid image file');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('Image file size cannot exceed 10MB');
      return;
    }

    // Preview selected image
    const imageUrl = await readFileAsDataURL(file);
    setSelectedImage(imageUrl);
    setSelectedFile(file);
  };

  // Read file as Data URL
  const readFileAsDataURL = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  // Handle image clear
  const handleClearImage = () => {
    setSelectedImage(null);
    setSelectedFile(null);
    setImageUrl('');
    setGeneratedImages([]);
    setActiveImageIndex(0);
    // 重置进度状态
    setProgress(0);
    setPerceptionProgress(0);
  };

  // Handle URL input
  const handleUrlSubmit = async () => {
    if (!imageUrl.trim()) {
      toast.error('Please enter an image URL');
      return;
    }

    try {
      setIsUploading(true);
      setUploadError(null); // Reset upload error state
      toast.info('Validating and uploading image...');

      // Generate unique filename
      const fileExtension = imageUrl.split('.').pop()?.toLowerCase() || 'jpg';
      const filename = `url-upload-${uuidv4()}.${fileExtension}`;
      
      // Call upload API to upload image to R2
      const response = await fetch('/api/upload-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sourceUrl: imageUrl,
          filename: filename,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Upload failed' }));
        throw new Error(errorData.error || 'Image upload failed');
      }

      const data = await response.json();
      
      if (!data.url) {
        throw new Error('Upload successful but no valid image URL returned');
      }
      
      // Use R2 returned link
      setSelectedImage(data.url);
      //toast.success('Image successfully uploaded to secure storage');
      //console.log('Image uploaded to R2, URL:', data.url);
    } catch (error) {
      console.error('URL processing error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Invalid image URL or upload failed';
      setUploadError(errorMessage); // Set upload error state
      toast.error(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  // Handle prompt change
  const handlePromptChange = async (value: string) => {
    //console.log('handlePromptChange called with:', value);
    setPrompt(value);
    // 用户手动输入了提示词，重置Quick Action标志
    setIsPromptFromQuickAction(false);
  };

  // 修改handleTranslateToggle，确保翻译始终启用
  const handleTranslateToggle = async (checked: boolean) => {
    // 保持翻译功能始终启用
    setTranslate(true);
  };

  // Handle quick action button click
  const handleQuickAction = (action: string) => {
    let actionPrompt = "";

    switch(action) {
      case "advanced-restore":
        actionPrompt = "Restore this old black-and-white photograph by repairing tears, folds, and missing parts. Remove scratches, dust, and stains. Sharpen and enhance fine details while keeping textures natural. Accurately colorize the image with realistic skin tones, clothing colors, and background hues. Adjust lighting and contrast for clarity, and preserve the authentic vintage style without over-smoothing or altering the subject's features.";
        break;
      case "enhance-quality":
        actionPrompt = "Enhance this photograph to high resolution with sharp, clear details. Remove noise, grain, and blur while preserving natural textures such as skin, hair, fabric, and background elements. Maintain accurate colors, realistic lighting, and authentic atmosphere. Improve clarity on faces, clothing, and important objects without over-smoothing. The result should look like a professionally scanned high-quality photograph, natural and true to life.";
        break;
      case "restore":
        actionPrompt = "This is a severely damaged old photo. Restore and colorize this image. Remove any damages or scratches or imperfections.";
        break;
      case "haircut":
        actionPrompt = "Change the hairstyle of the person in this photo to a new style haircut. Maintain the rest of the image the same, and do not modify the background or the proportions of the character's body.";
        break;
      case "portrait":
        actionPrompt = "Create a portrait of the person in this photo, featuring different styles and poses.";
        break;
      case "watermark":
        actionPrompt = "Remove **only the visible watermarks** (such as superimposed text, logos, repeating patterns, or other overlay graphics) from this image. **Crucially, preserve all integral textual elements that are part of the original scene content (e.g., signs, labels, book titles, screen text, handwritten notes).** Seamlessly reconstruct the underlying image details where the watermarks were present, focusing on areas **typically used for watermarks (like corners, edges, or repetitive backgrounds)**. Ensure the final image is clean, with original textures, colors, and overall quality perfectly preserved. The image must be free of any removal artifacts, smudging, or unintended deletion of non-watermark elements.";
        break;
      default:
        return;
    }

    setPrompt(actionPrompt);
    // 设置标志，表示提示词来自Quick Action
    setIsPromptFromQuickAction(true);
    // Clear translated prompt, as original prompt has changed
    setTranslatedPrompt("");
  };

  // Function to translate text
  const translateText = async (text: string): Promise<string> => {
    if (!text.trim()) return "";
    
    try {
      setIsTranslating(true);
      //console.log('Sending translation request...');
      
      // Set timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 20000); // 20 second timeout
      
      const response = await fetch('/api/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text }),
        signal: controller.signal
      }).finally(() => clearTimeout(timeoutId));
      
      console.log('Got response:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        //console.error('Translation API error:', errorData);
        throw new Error(errorData.error || 'Translation failed');
      }

      const data = await response.json();
      //console.log('Translation response data:', data);
      
      if (!data.translatedText) {
        throw new Error('No translation returned');
      }
      
      //console.log('Translation successful:', data.translatedText);
      return data.translatedText;
    } catch (error: any) {
      //console.error('Translation error:', error);
      // If timeout error, show specific message
      const errorMessage = error.name === 'AbortError' 
        ? 'Translation request timed out, please try again' 
        : (error instanceof Error ? error.message : 'Translation failed');
        
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsTranslating(false);
    }
  };

  // 修改handleGenerate函数，添加40秒延时
  const handleGenerate = async () => {
    if (!selectedImage) {
      toast.error('Please upload an image or provide an image URL');
      return;
    }

    // 确定最终使用的提示词
    let finalPrompt = prompt;

    // 如果显示上色风格选择器且选择了上色风格，使用风格的 prompt
    if (showColorizationStyles && selectedStyle) {
      const selectedStyleData = colorStyleCategories
        .flatMap(category => category.styles)
        .find(style => style.id === selectedStyle);

      if (selectedStyleData) {
        finalPrompt = selectedStyleData.prompt;
      }
    }

    // 如果不显示提示词输入框但有默认提示词，则使用默认提示词
    if (!showPromptInput && !finalPrompt && defaultPrompt) {
      finalPrompt = defaultPrompt;
    }

    if (!finalPrompt) {
      toast.error('Please enter a prompt or select a colorization style');
      return;
    }

    // 更新显示的 prompt 为最终使用的 prompt
    setPrompt(finalPrompt);

    // 确保每次生成前重置进度状态
    setProgress(0);
    setPerceptionProgress(0);
    setActualProcessingStarted(false);

    // 立即设置生成状态，开始感知进度条
    setIsGenerating(true);

    // 等待40秒后开始真正的处理
    setTimeout(async () => {
      await performActualGeneration();
    }, 40000); // 40秒延时
  };

  // 实际的图片生成处理函数
  const performActualGeneration = async () => {
    setActualProcessingStarted(true);
    setProgress(60); // 从60%开始真实进度

    // 确定最终使用的提示词
    let finalPrompt = prompt;

    // 如果显示上色风格选择器且选择了上色风格，使用风格的 prompt
    if (showColorizationStyles && selectedStyle) {
      const selectedStyleData = colorStyleCategories
        .flatMap(category => category.styles)
        .find(style => style.id === selectedStyle);

      if (selectedStyleData) {
        finalPrompt = selectedStyleData.prompt;
      }
    }

    // 如果不显示提示词输入框但有默认提示词，则使用默认提示词
    if (!showPromptInput && !finalPrompt && defaultPrompt) {
      finalPrompt = defaultPrompt;
    }

    // Check if user has enough credits
    if (userCredits !== null && userCredits < 3) {
      toast.error('Insufficient credits. Each image generation requires 3 credits');
      router.push('/pricing'); // Navigate to recharge page
      setIsGenerating(false); // 重置生成状态
      return;
    }

    try {
      // Generate unique task ID
      const taskId = `restoreoldphotos_${uuidv4()}`;

      // 设置进度到61%，表示开始处理
      setProgress(61);

      // First deduct credits, each image generation costs 3 credits
      try {
        const deductResponse = await fetch('/api/deduct-credits', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            credits: 3, // Deduct 3 credits
            taskId: taskId, // Use task ID
          }),
        });

        if (!deductResponse.ok) {
          const errorData = await deductResponse.json();
          if (errorData.error === "insufficient_credits") {
            toast.error('Insufficient credits. Please recharge and try again');
            router.push('/pricing'); // Navigate to recharge page
            return;
          }
          throw new Error(errorData.error || 'Failed to deduct credits');
        }

        const creditData = await deductResponse.json();
        setUserCredits(creditData.creditsLeft);
        console.log('Credits deducted successfully, remaining credits:', creditData.creditsLeft);

        // 更新进度条到62%
        setProgress(62);
      } catch (creditError) {
        console.error('Failed to deduct credits:', creditError);
        toast.error('Failed to deduct credits, please try again later');
        setIsGenerating(false); // 重置生成状态
        return; // Credit deduction failed, abort task
      }

      // 设置进度到63%，表示开始准备提示词
      setProgress(63);

      // 直接使用用户输入的提示词，不进行翻译
      let finalPrompt = prompt;

      // 设置进度到64%，表示提示词准备完成
      setProgress(64);

      // 设置进度到65%，表示开始处理图片
      setProgress(65);

      // Get image URL
      // selectedImage might already be an R2 URL (uploaded via handleUrlSubmit)
      // or a local file Data URL
      let uploadedImageUrl = selectedImage;

      // If it's a local file or Data URL, need to upload to R2
      if (selectedFile || (selectedImage && !selectedImage.startsWith('http'))) {
        setIsUploading(true);

        let sourceUrl = selectedImage;
        // If it's a Data URL, ensure format is correct
        if (selectedFile) {
          sourceUrl = await readFileAsDataURL(selectedFile);
        }

        try {
          const uploadResponse = await fetch('/api/upload-image', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              sourceUrl: sourceUrl,
              filename: `damaged-photo-${uuidv4()}.${selectedFile ? selectedFile.name.split('.').pop() : 'png'}`,
            }),
            credentials: 'include', // Ensure authentication info is included
          });

          if (!uploadResponse.ok) {
            const errorData = await uploadResponse.json();
            throw new Error(errorData.error || 'Image upload failed');
          }

          const data = await uploadResponse.json();
          if (!data.url) {
            throw new Error('Invalid upload response format');
          }
          uploadedImageUrl = data.url;
          // Upload complete, update progress to 66%
          setProgress(66);
        } catch (uploadError) {
          console.error('Upload error:', uploadError);
          await refundCredits(taskId); // Refund credits
          throw new Error(uploadError instanceof Error ? uploadError.message : 'Image upload failed');
        } finally {
          setIsUploading(false);
        }
      } else {
        // 如果不需要上传图片，也更新进度条
        setProgress(66);
      }

      // 设置进度到67%，表示开始生成图片
      setProgress(67);

      // API request starts
      const response = await fetch('/api/kontext-dev/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: uploadedImageUrl,
          prompt: finalPrompt,
          aspectRatio: aspectRatio,
          outputFormat: outputFormat,
          addWatermark: showWatermark,
          imageCount: imageCount, // Add image count parameter
          taskId: taskId, // Add task ID
        }),
        credentials: 'include', // Ensure authentication info is included
      });

      if (!response.ok) {
        const errorData = await response.json();
        // Request failed, refund credits
        await refundCredits(taskId);
        throw new Error(errorData.error || 'Image generation failed');
      }

      // API request successful, update progress to 70%
      setProgress(70);

      const data = await response.json();
      if (data.status === 'failed') {
        // API returned failure status, refund credits
        await refundCredits(taskId);
        throw new Error(data.error || 'Image generation failed');
      }
      
      console.log('Generation response:', data);

      // 处理响应数据，更新进度到75%
      setProgress(75);

      // Check output field
      if (!data.output) {
        await refundCredits(taskId);
        throw new Error('Missing output field in generation response');
      }

      // Ensure output is an array and not empty
      if (!Array.isArray(data.output) || data.output.length === 0) {
        await refundCredits(taskId);
        throw new Error('Empty output array in generation result');
      }

      // Ensure all output items are valid URLs
      const validOutputUrls = data.output.filter(
        (url: any) => typeof url === 'string' && url.startsWith('http')
      );

      // 保存原始输出 URL 作为备用
      let originalUrls: string[] = [];
      if (data.original_output && Array.isArray(data.original_output)) {
        originalUrls = data.original_output.filter(
          (url: any) => typeof url === 'string' && url.startsWith('http')
        );
        console.log('Original output URLs:', originalUrls);
        setOriginalImageUrls(originalUrls);
      }

      // 更新进度到80%，表示即将完成
      setProgress(80);
      
      if (validOutputUrls.length === 0) {
        // 如果没有有效的输出 URL，但有原始 URL，则使用原始 URL
        if (originalUrls.length > 0) {
          console.log('No valid output URLs, using original URLs instead');

          // 处理水印逻辑
          try {
            console.log('🎯 Starting watermark processing for original URLs...');
            const processedImages = await processImagesWithWatermark(originalUrls, taskId, showWatermark);
            console.log('🎯 Watermark processing completed for original URLs');
            setGeneratedImages(processedImages);
            setActiveImageIndex(0);
          } catch (watermarkError) {
            console.error('❌ Watermark processing failed for original URLs:', watermarkError);
            setGeneratedImages(originalUrls);
            setActiveImageIndex(0);
          }
        } else {
          await refundCredits(taskId);
          throw new Error('No valid image URLs were generated');
        }
      } else {
        // 添加日志输出，显示收到的 URL
        console.log('Received image URLs:', validOutputUrls);
        console.log('First URL type:', typeof validOutputUrls[0]);

        // 处理水印逻辑
        try {
          console.log('🎯 Starting watermark processing for generated images...');
          const processedImages = await processImagesWithWatermark(validOutputUrls, taskId, showWatermark);
          console.log('🎯 Watermark processing completed for generated images');
          setGeneratedImages(processedImages);
          setActiveImageIndex(0);
        } catch (watermarkError) {
          console.error('❌ Watermark processing failed for generated images:', watermarkError);
          setGeneratedImages(validOutputUrls);
          setActiveImageIndex(0);
        }
      }
      
      // 更新进度到85%，表示保存记录
      setProgress(85);

      // Save generation record to database
      if (validOutputUrls.length > 0 || originalUrls.length > 0) {
        try {
          // Since we've already updated the record in the backend, this serves as a backup mechanism
          // If backend update fails, frontend will still try to update
          const updateResult = await updateRecordStatus(taskId, validOutputUrls[0] || originalUrls[0]);
          if (updateResult) {
            console.log('Frontend record status update successful');
          } else {
            console.log('Frontend record status update failed, but backend may have successfully updated');
          }
        } catch (dbError) {
          console.error('Failed to save record to database:', dbError);
          // Don't block user from continuing, as image has been successfully generated
        }
      }

      // 设置进度到100%，表示完成
      setProgress(100);
    } catch (error) {
      //console.error('Generation error:', error);
      toast.error(error instanceof Error ? error.message : 'Image generation failed');
    } finally {
      setIsGenerating(false);
    }
  };

  // Function to refund credits
  const refundCredits = async (taskId: string) => {
    try {
      //console.log(`Task failed, preparing to refund credits, task ID: ${taskId}`);
      
      // Call refund credits API
      const response = await fetch('/api/refund-credits', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          credits: 3, // Refund 3 credits
          taskId: taskId,
          reason: 'generation_failed'
        }),
      });
      
      if (!response.ok) {
        console.error('Failed to refund credits');
        toast.error('Generation failed, there was a problem with the credit refund process, please contact customer support');
        return;
      }
      
      const data = await response.json();
      setUserCredits(data.creditsLeft);
      console.log('Credits refunded successfully, current credits:', data.creditsLeft);
      toast.info('Generation failed, 3 credits have been automatically refunded');
    } catch (error) {
      console.error('Error during credit refund process:', error);
      toast.error('Failed to refund credits, please contact customer support');
    }
  };

  // Update database record status
  const updateRecordStatus = async (taskId: string, imageUrl: string) => {
    try {
      console.log(`Preparing to update task record status, task ID: ${taskId}`);
      
      // Get user information (nickname and email)
      try {
        // We don't need to call Supabase directly here, as we can get and update user info through the API
        // Backend API will handle getting this information
      } catch (userError) {
        console.error('Error getting user information:', userError);
        // Continue processing, don't block the process
      }
      
      // Call API to update record status
      const response = await fetch(`/api/kontext-dev/update-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskId,
          status: 'COMPLETED',
          generatedImageUrl: imageUrl,
          completed_at: new Date().toISOString(), // Add completion time field
          includeUserInfo: true // Add flag to request backend to include user info
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Failed to parse response' }));
        console.error('Failed to update record status:', errorData);
        return false;
      }
      
      const data = await response.json();
      console.log('Record status updated successfully:', data);
      return true;
    } catch (error) {
      console.error('Error updating record status:', error);
      return false;
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-4 grid grid-cols-1 md:grid-cols-2 gap-8">
      {/* Left panel */}
      <Card className="p-6 space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-semibold">{title}</h2>
          <div className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full flex items-center gap-2">
            <span className="text-sm font-normal">Credits:</span>{" "}
            {mounted && session ? (
              <span className="text-lg font-bold">{userCredits !== null ? userCredits : "Loading..."}</span>
            ) : (
              <span className="text-lg font-bold">
                <a href="/auth/signin" className="hover:underline">Sign in</a>
              </span>
            )}
            <svg
              className="w-4 h-4 text-amber-500 fill-current"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
            >
              <path d="M12 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0-15c-4.97 0-9 4.03-9 9v7c0 1.66 1.34 3 3 3h12c1.66 0 3-1.34 3-3v-7c0-4.97-4.03-9-9-9zm0 3c3.31 0 6 2.69 6 6v7c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1v-7c0-3.31 2.69-6 6-6z" />
            </svg>
          </div>
        </div>
        
        {/* Unlogged user prompt */}
        {mounted && !session && (
          <div className="text-center py-2 bg-blue-50 rounded-lg mb-4">
            <a 
              href="/auth/signin" 
              className="text-blue-600 hover:text-blue-800 underline text-sm"
            >
              <strong>Sign in for free trial</strong>
            </a>
          </div>
        )}
        
        <div className="space-y-4">
          <div>
            <Label>Image</Label>
            <Tabs defaultValue="upload" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="upload">Upload Image</TabsTrigger>
                <TabsTrigger 
                  value="url" 
                  onClick={() => setUploadError(null)}
                >
                  Image URL
                </TabsTrigger>
              </TabsList>
              <TabsContent value="upload">
                <div className="relative min-h-[300px] rounded-lg border-2 border-dashed border-muted-foreground/25 hover:border-muted-foreground/50 transition-colors">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                    disabled={isUploading}
                  />
                  <div className="absolute inset-0 flex flex-col items-center justify-center gap-2">
                    {selectedImage ? (
                      <div className="relative w-full h-full">
                        <Image
                          src={selectedImage}
                          alt="Uploaded image"
                          className="object-contain"
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          priority
                        />
                        <Button
                          variant="secondary"
                          size="icon"
                          className="absolute top-2 right-2 bg-background/80 backdrop-blur-sm hover:bg-background/90 z-20"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleClearImage();
                          }}
                        >
                          <X className="h-4 w-4" />
                          <span className="sr-only">Clear image</span>
                        </Button>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center gap-2 text-muted-foreground hover:text-muted-foreground/80 transition-colors">
                        {isUploading ? (
                          <>
                            <svg className="animate-spin h-8 w-8" viewBox="0 0 24 24">
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                                fill="none"
                              />
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              />
                            </svg>
                            <p className="text-sm font-medium">Uploading...</p>
                          </>
                        ) : (
                          <>
                            <Upload className="w-8 h-8" />
                            <p className="text-sm font-medium">Upload Image</p>
                            <p className="text-xs text-muted-foreground/70">Click or drag and drop</p>
                          </>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="url">
                <div className="space-y-2">
                  <Input
                    type="url"
                    placeholder="Enter image URL"
                    value={imageUrl}
                    onChange={(e) => {
                      setImageUrl(e.target.value);
                      // When user clears input field, reset error state
                      if (!e.target.value.trim()) {
                        setUploadError(null);
                      }
                    }}
                    disabled={isUploading}
                  />
                  <Button 
                    onClick={handleUrlSubmit}
                    className="w-full"
                    disabled={isUploading}
                  >
                    {isUploading ? (
                      <span className="flex items-center gap-2">
                        <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                            fill="none"
                          />
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          />
                        </svg>
                        Uploading...
                      </span>
                    ) : 'Validate & Upload URL'}
                  </Button>
                  
                  {/* Upload status prompt */}
                  {selectedImage && selectedImage.includes('img.restore-old-photos.com') ? (
                    <div className="mt-2">
                      <p className="text-xs text-green-600 mb-2 flex items-center">
                        <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <circle cx="12" cy="12" r="9" stroke="currentColor" strokeWidth="2"/>
                        </svg>
                        Image successfully uploaded to secure storage
                      </p>
                      
                      {/* Uploaded image preview */}
                      <div className="relative w-full h-40 rounded-md overflow-hidden border border-muted">
                        <Image
                          src={selectedImage}
                          alt="Uploaded image preview"
                          className="object-contain"
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                      </div>
                    </div>
                  ) : uploadError ? (
                    <div className="mt-2">
                      <p className="text-xs text-red-600 mb-2 flex items-center">
                        <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 8v4m0 4h.01M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2s10 4.477 10 10z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        Upload failed: {uploadError}
                      </p>
                      <div className="p-4 bg-red-50 rounded-md text-sm text-red-800">
                        <p className="font-medium mb-1">Possible reasons:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li>Image URL may have CORS restrictions</li>
                          <li>Image format not supported</li>
                          <li>Image size exceeds limits</li>
                          <li>Network connectivity issues</li>
                        </ul>
                        <p className="mt-2">Suggestion: Try downloading the image and upload via the "Upload Image" tab</p>
                      </div>
                    </div>
                  ) : (
                    imageUrl.trim() && !isUploading && (
                      <p className="text-xs text-amber-600 mt-2 flex items-center">
                        <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 8v4m0 4h.01M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2s10 4.477 10 10z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        Please click the button above to validate and upload the image
                      </p>
                    )
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* 根据showPromptInput参数控制提示词输入框的显示 */}
          {showPromptInput && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Prompt</Label>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 text-muted-foreground hover:text-foreground"
                    onClick={() => {
                      setPrompt("");
                      setTranslatedPrompt("");
                    }}
                    disabled={isGenerating || !prompt}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                      <path d="M12 20h9"></path>
                      <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                    </svg>
                    Clear
                  </Button>
                </div>
              </div>
              
              <p className="text-xs text-amber-600 font-medium">Only English prompts will work effectively</p>
              
              <Textarea
                placeholder="Write your prompt in English here, other languages won't work. For the best results, please provide a detailed description of your desired image."
                className="min-h-[150px] resize-y"
                value={prompt}
                onChange={(e) => handlePromptChange(e.target.value)}
                disabled={isGenerating}
              />
            </div>
          )}

          {/* 根据showQuickActions参数控制快速操作按钮的显示 */}
          {showQuickActions && (
            <div>
              <Label>Quick Actions</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction("advanced-restore")}
                >
                  Advanced Restore Image
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction("enhance-quality")}
                >
                  Enhance Quality
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction("restore")}
                >
                  Basic Restore Image
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction("haircut")}
                >
                  Change Haircut
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction("portrait")}
                >
                  Portrait Series
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction("watermark")}
                >
                  Remove Watermark
                </Button>
              </div>
            </div>
          )}

          {/* 根据showImageSettings参数控制图像设置的显示 */}
          {showImageSettings && (
            <div className="space-y-4">
              <div>
                <Label>Image Dimensions</Label>
                <div className="space-y-1 sm:space-y-2">
                  <div className="grid grid-cols-5 gap-1 sm:gap-2 mt-1 sm:mt-2">
                    <Button 
                      variant={aspectRatio === "match_input_image" ? "default" : "outline"} 
                      className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                      onClick={() => setAspectRatio("match_input_image")}
                    >
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center">
                          <span className="text-[10px] sm:text-xs block">Match</span>
                          <span className="text-[10px] sm:text-xs block">Input</span>
                        </div>
                      </div>
                    </Button>
                    <Button 
                      variant={aspectRatio === "2:3" ? "default" : "outline"}
                      className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                      onClick={() => setAspectRatio("2:3")}
                    >
                      <div className="flex-grow flex items-end justify-center">
                        <div style={{ width: '10px', height: '15px', border: '1px solid currentColor' }} className="transform scale-125 sm:scale-150 md:scale-[1.8] mb-0 sm:mb-1"></div>
                      </div>
                      <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">2:3</span>
                    </Button>
                    <Button 
                      variant={aspectRatio === "16:9" ? "default" : "outline"}
                      className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                      onClick={() => setAspectRatio("16:9")}
                    >
                      <div className="flex-grow flex items-end justify-center">
                        <div style={{ width: '16px', height: '9px', border: '1px solid currentColor' }} className="transform scale-125 sm:scale-150 md:scale-[1.8] mb-0 sm:mb-1"></div>
                      </div>
                      <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">16:9</span>
                    </Button>
                    <Button 
                      variant={aspectRatio === "1:1" ? "default" : "outline"}
                      className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                      onClick={() => setAspectRatio("1:1")}
                    >
                      <div className="flex-grow flex items-end justify-center">
                        <div style={{ width: '14px', height: '14px', border: '1px solid currentColor' }} className="transform scale-110 sm:scale-125 md:scale-150 mb-0 sm:mb-1"></div>
                      </div>
                      <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">1:1</span>
                    </Button>
                    <Button 
                      variant={aspectRatio === "9:16" ? "default" : "outline"}
                      className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                      onClick={() => setAspectRatio("9:16")}
                    >
                      <div className="flex-grow flex items-end justify-center">
                        <div style={{ width: '9px', height: '16px', border: '1px solid currentColor' }} className="transform scale-125 sm:scale-150 md:scale-[1.8] mb-0 sm:mb-1"></div>
                      </div>
                      <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">9:16</span>
                    </Button>
                  </div>
                  
                  {showMoreAspectRatios && (
                    <div className="grid grid-cols-5 gap-1 sm:gap-2 mt-1 sm:mt-2">
                      <Button 
                        key="3:2"
                        variant={aspectRatio === "3:2" ? "default" : "outline"}
                        className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                        onClick={() => setAspectRatio("3:2")}
                      >
                        <div className="flex-grow flex items-end justify-center">
                          <div 
                            style={{ 
                              width: '15px', 
                              height: '10px', 
                              border: '1px solid currentColor'
                            }}
                            className="transform scale-110 sm:scale-140 md:scale-[1.7] mb-0 sm:mb-1"
                          ></div>
                        </div>
                        <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">3:2</span>
                      </Button>
                      <Button 
                        key="9:21"
                        variant={aspectRatio === "9:21" ? "default" : "outline"}
                        className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                        onClick={() => setAspectRatio("9:21")}
                      >
                        <div className="flex-grow flex items-end justify-center">
                          <div 
                            style={{ 
                              width: '9px', 
                              height: '21px', 
                              border: '1px solid currentColor'
                            }}
                            className="transform scale-90 sm:scale-110 md:scale-125 mb-0 sm:mb-1"
                          ></div>
                        </div>
                        <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">9:21</span>
                      </Button>
                      <Button 
                        key="4:5"
                        variant={aspectRatio === "4:5" ? "default" : "outline"}
                        className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                        onClick={() => setAspectRatio("4:5")}
                      >
                        <div className="flex-grow flex items-end justify-center">
                          <div 
                            style={{ 
                              width: '12px', 
                              height: '15px', 
                              border: '1px solid currentColor'
                            }}
                            className="transform scale-110 sm:scale-125 md:scale-150 mb-0 sm:mb-1"
                          ></div>
                        </div>
                        <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">4:5</span>
                      </Button>
                      <Button 
                        key="21:9"
                        variant={aspectRatio === "21:9" ? "default" : "outline"}
                        className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                        onClick={() => setAspectRatio("21:9")}
                      >
                        <div className="flex-grow flex items-end justify-center">
                          <div 
                            style={{ 
                              width: '21px', 
                              height: '9px', 
                              border: '1px solid currentColor'
                            }}
                            className="transform scale-90 sm:scale-110 md:scale-125 mb-0 sm:mb-1"
                          ></div>
                        </div>
                        <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">21:9</span>
                      </Button>
                      <Button 
                        key="3:4"
                        variant={aspectRatio === "3:4" ? "default" : "outline"}
                        className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                        onClick={() => setAspectRatio("3:4")}
                      >
                        <div className="flex-grow flex items-end justify-center">
                          <div 
                            style={{ 
                              width: '9px', 
                              height: '12px', 
                              border: '1px solid currentColor'
                            }}
                            className="transform scale-110 sm:scale-140 md:scale-[1.7] mb-0 sm:mb-1"
                          ></div>
                        </div>
                        <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">3:4</span>
                      </Button>
                      <Button 
                        key="4:3"
                        variant={aspectRatio === "4:3" ? "default" : "outline"}
                        className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                        onClick={() => setAspectRatio("4:3")}
                      >
                        <div className="flex-grow flex items-end justify-center">
                          <div 
                            style={{ 
                              width: '12px', 
                              height: '9px', 
                              border: '1px solid currentColor'
                            }}
                            className="transform scale-110 sm:scale-140 md:scale-[1.7] mb-0 sm:mb-1"
                          ></div>
                        </div>
                        <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">4:3</span>
                      </Button>
                      <Button 
                        key="1:2"
                        variant={aspectRatio === "1:2" ? "default" : "outline"}
                        className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                        onClick={() => setAspectRatio("1:2")}
                      >
                        <div className="flex-grow flex items-end justify-center">
                          <div 
                            style={{ 
                              width: '8px', 
                              height: '16px', 
                              border: '1px solid currentColor'
                            }}
                            className="transform scale-110 sm:scale-125 md:scale-150 mb-0 sm:mb-1"
                          ></div>
                        </div>
                        <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">1:2</span>
                      </Button>
                      <Button 
                        key="2:1"
                        variant={aspectRatio === "2:1" ? "default" : "outline"}
                        className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                        onClick={() => setAspectRatio("2:1")}
                      >
                        <div className="flex-grow flex items-end justify-center">
                          <div 
                            style={{ 
                              width: '16px', 
                              height: '8px', 
                              border: '1px solid currentColor'
                            }}
                            className="transform scale-110 sm:scale-125 md:scale-150 mb-0 sm:mb-1"
                          ></div>
                        </div>
                        <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">2:1</span>
                      </Button>
                      <Button 
                        key="5:4"
                        variant={aspectRatio === "5:4" ? "default" : "outline"}
                        className="h-14 sm:h-16 md:h-20 aspect-square flex flex-col items-center justify-center p-0.5 sm:p-1 md:p-2"
                        onClick={() => setAspectRatio("5:4")}
                      >
                        <div className="flex-grow flex items-end justify-center">
                          <div 
                            style={{ 
                              width: '15px', 
                              height: '12px', 
                              border: '1px solid currentColor'
                            }}
                            className="transform scale-110 sm:scale-125 md:scale-150 mb-0 sm:mb-1"
                          ></div>
                        </div>
                        <span className="text-[10px] sm:text-xs mt-0 sm:mt-1">5:4</span>
                      </Button>
                    </div>
                  )}
                  
                  <div className="flex justify-center mt-1 sm:mt-2">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => setShowMoreAspectRatios(!showMoreAspectRatios)}
                      className="flex items-center gap-1 text-muted-foreground"
                    >
                      {showMoreAspectRatios ? (
                        <>
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 15L12 9L6 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                          Fewer Options
                        </>
                      ) : (
                        <>
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                          More Options
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>

              {/* 根据showColorizationStyles参数控制上色风格选择器的显示 */}
              {showColorizationStyles && (
                <div>
                  <div className="flex items-center justify-between">
                    <Label>Colorization Style</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowStyleSelector(!showStyleSelector)}
                    className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
                  >
                    {showStyleSelector ? "Hide Styles" : "Choose Style"}
                    <svg
                      className={`ml-1 h-3 w-3 transition-transform ${showStyleSelector ? 'rotate-180' : ''}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </Button>
                </div>

                {selectedStyle && (
                  <div className="mt-2 p-2 bg-muted rounded text-xs text-muted-foreground">
                    <strong>Selected:</strong> {colorStyleCategories
                      .flatMap(category => category.styles)
                      .find(style => style.id === selectedStyle)?.name}
                  </div>
                )}

                {showStyleSelector && (
                  <div className="space-y-3 mt-2 max-h-96 overflow-y-auto border rounded-lg p-3">
                    {colorStyleCategories.map((category) => (
                      <div key={category.category} className="space-y-2">
                        <h4 className="text-sm font-semibold text-foreground border-b pb-1">{category.category}</h4>
                        <div className="grid grid-cols-1 gap-1">
                          {category.styles.map((style) => (
                            <Button
                              key={style.id}
                              variant={selectedStyle === style.id ? "default" : "ghost"}
                              size="sm"
                              className="justify-start text-left h-auto py-1.5 px-2 text-xs hover:bg-muted"
                              onClick={() => {
                                const newStyleId = selectedStyle === style.id ? "" : style.id;
                                setSelectedStyle(newStyleId);

                                // 如果显示提示词输入框，更新提示词
                                if (showPromptInput && newStyleId) {
                                  setPrompt(style.prompt);
                                } else if (showPromptInput && !newStyleId) {
                                  setPrompt("");
                                }

                                setShowStyleSelector(false);
                              }}
                            >
                              {style.name}
                            </Button>
                          ))}
                        </div>
                      </div>
                    ))}
                    {selectedStyle && (
                      <div className="mt-3 pt-2 border-t">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedStyle("");
                            if (showPromptInput) {
                              setPrompt("");
                            }
                            setShowStyleSelector(false);
                          }}
                          className="w-full text-xs"
                        >
                          Clear Selection
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </div>
              )}

              <div>
                <Label>Output Format</Label>
                <div className="grid grid-cols-3 gap-2 mt-2">
                  {["png", "jpg", "webp"].map((format) => (
                    <Button 
                      key={format}
                      variant={outputFormat === format ? "default" : "outline"} 
                      size="sm"
                      onClick={() => setOutputFormat(format)}
                    >
                      {format.toUpperCase()}
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <Label>Number of Images</Label>
                <div className="grid grid-cols-5 gap-2 mt-2">
                  {[1, 2, 3, 4, 5].map((num) => (
                    <Button 
                      key={num} 
                      variant={imageCount === num ? "default" : "outline"} 
                      size="sm"
                      className="relative"
                      onClick={() => handleImageCountChange(num)}
                    >
                      {num}
                      {num > 1 && (
                        <div className="absolute -top-2 -right-1">
                          <svg className="w-4 h-4 text-yellow-500" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 16L3 5l5 4 4-6 4 6 5-4-2 11H5z" />
                            <path d="M19 16H5v3a1 1 0 001 1h12a1 1 0 001-1v-3z" />
                          </svg>
                        </div>
                      )}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 根据showWatermarkToggle参数控制水印切换的显示 */}
          {showWatermarkToggle && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label htmlFor="watermark-toggle">Add Watermark</Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-[200px]">
                      <p>
                        {userNeedsWatermark
                          ? `Upgrade to remove watermarks.`
                          : `Upgrade to remove watermarks from your generated images`
                        }
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  id="watermark-toggle"
                  checked={showWatermark}
                  onCheckedChange={handleWatermarkToggle}
                  disabled={isGenerating || userNeedsWatermark || (!isUserPro && showWatermark)}
                />
                {userNeedsWatermark && (
                  <Button
                    size="sm"
                    className="text-xs px-3 py-1 h-7 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 rounded-full"
                    onClick={() => window.open('/pricing', '_blank')}
                  >
                    Upgrade
                  </Button>
                )}
              </div>
            </div>
          )}
          
          <Button
            className="w-full"
            size="lg"
            onClick={handleGenerate}
            disabled={isGenerating || !selectedImage || (!showPromptInput && !defaultPrompt && !prompt && !selectedStyle)}
          >
            {isGenerating ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {perceptionProgress > 0 ? `${generateButtonText}... ${Math.round(perceptionProgress)}%` : `${generateButtonText}...`}
              </>
            ) : (
              generateButtonText
            )}
          </Button>
          
          {isGenerating && (
            <>
              <div className="w-full bg-muted rounded-full h-2.5 mt-2 overflow-hidden">
                <div 
                  className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                  style={{ width: `${perceptionProgress}%` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-[shimmer_2s_infinite]"></div>
                </div>
              </div>
              <p className="text-xs text-muted-foreground text-center mt-1">
                Generating...
              </p>
              <p className="text-xs text-red-600 text-center mt-2 font-medium">
                Your image is being generated. This typically takes 20s-2 minutes. Please don't close this page.
              </p>
            </>
          )}
        </div>
      </Card>

      {/* Right panel */}
      <Card className="p-6 space-y-6">
        <h2 className="text-2xl font-semibold">{resultTitle}</h2>
        <p className="text-muted-foreground">Your edited image will appear here</p>
        <div className="text-sm">
          Result Time <span className="font-semibold">10s - 40s</span>
        </div>
        
        <div className="relative aspect-[4/3] w-full bg-muted rounded-lg overflow-hidden">
          {generatedImages.length > 0 ? (
            <Image
              src={generatedImages[activeImageIndex]}
              alt="Generated image"
              fill
              className="object-contain"
              priority
              onError={(e) => {
                console.error('Image load error:', e);
                // 尝试使用原始 URL 作为回退
                if (originalImageUrls && originalImageUrls.length > activeImageIndex) {
                  console.log('Falling back to original URL:', originalImageUrls[activeImageIndex]);
                  e.currentTarget.src = originalImageUrls[activeImageIndex];
                }
              }}
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center">
              <p className="text-muted-foreground">
                {isGenerating ? 'Generating...' : 'Result will be displayed here'}
              </p>
            </div>
          )}
        </div>
        
        {/* Image navigation buttons */}
        {generatedImages.length > 1 && (
          <div className="flex justify-center gap-2">
            {generatedImages.map((_, index) => (
              <Button 
                key={index}
                variant={activeImageIndex === index ? "default" : "outline"}
                size="icon"
                className="w-8 h-8"
                onClick={() => setActiveImageIndex(index)}
              >
                {index + 1}
              </Button>
            ))}
          </div>
        )}

        {generatedImages.length > 0 && (
          <>
            <Button
              className="w-full"
              size="lg"
              onClick={() => {
                // 直接在新窗口打开图片
                let urlToView = generatedImages[activeImageIndex];

                if (!urlToView) {
                  toast.error('No image URL available');
                  return;
                }

                // 检查是否有原始 URL 可用作备用
                if (originalImageUrls && originalImageUrls.length > activeImageIndex) {
                  const originalUrl = originalImageUrls[activeImageIndex];
                  console.log("Opening image in new window, trying R2 URL first:", urlToView);

                  // 先尝试打开 R2 URL，如果失败则尝试原始 URL
                  const newWindow = window.open(urlToView, '_blank');

                  // 如果窗口被阻止或无法打开，尝试原始 URL
                  if (!newWindow) {
                    console.log("R2 URL failed, trying original URL:", originalUrl);
                    window.open(originalUrl, '_blank');
                  }
                } else {
                  // 如果没有原始 URL，直接打开生成的 URL
                  console.log("Opening image in new window:", urlToView);
                  window.open(urlToView, '_blank');
                }
              }}
            >
              <span className="flex items-center gap-2">
                <svg 
                  className="w-5 h-5" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
                View Full Image
              </span>
            </Button>
            
            <Button 
              className="w-full bg-blue-600 hover:bg-blue-700 text-white mt-2" 
              size="lg"
              onClick={async () => {
                try {
                  console.log("Starting image download...");
                  // 优先使用生成的 URL，如果无法访问则回退到原始 URL
                  let urlToDownload = generatedImages[activeImageIndex];
                  console.log("Original download URL:", urlToDownload.substring(0, 100) + "...");
                  
                  if (!urlToDownload) {
                    console.error("No image URL available");
                    toast.error('No image URL available');
                    return;
                  }
                  
                  // 检查是否有原始 URL 可用作备用
                  const hasOriginalUrl = originalImageUrls && originalImageUrls.length > activeImageIndex;
                  
                  // Use image proxy API to download, avoiding CORS issues
                  console.log("Preparing to download image via proxy...");
                  toast.info("Preparing download...", { duration: 2000 });
                  
                  // Use proxy API for download
                  const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(urlToDownload)}`;
                  console.log("Proxy URL:", proxyUrl);
                  
                  // Create a hidden anchor tag to trigger download
                  const link = document.createElement('a');
                  link.href = proxyUrl;
                  link.download = 'kontext-dev-image.png';
                  link.target = '_blank'; // Open in new tab to avoid navigation issues
                  console.log("Ready to trigger download...");
                  document.body.appendChild(link);
                  link.click();
                  
                  // Delay removing link
                  setTimeout(() => {
                    document.body.removeChild(link);
                    console.log("Download link removed");
                  }, 1000);
                  
                  console.log("Download process completed");
                  toast.success("Image download started");
                } catch (error) {
                  console.error('Failed to download image:', error);
                  toast.error(`Download failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                  
                  // Provide fallback download method
                  console.log("Trying fallback download method...");
                  // 尝试使用原始 URL 作为回退
                  let urlToDownload = generatedImages[activeImageIndex];
                  if (originalImageUrls && originalImageUrls.length > activeImageIndex) {
                    urlToDownload = originalImageUrls[activeImageIndex];
                    console.log("Using original URL for fallback:", urlToDownload);
                  }
                  
                  if (urlToDownload) {
                    // Open image in new window, user can right-click to save
                    window.open(urlToDownload, '_blank');
                    toast.info("Please right-click on the image in the new window and select 'Save Image As...' to download");
                  }
                }
              }}
            >
              <span className="flex items-center gap-2">
                <svg 
                  className="w-5 h-5" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                  />
                </svg>
                Download Image
              </span>
            </Button>
            
            {/* 添加下载提示 */}
            <p className="text-xs text-muted-foreground text-center mt-2">
              If download doesn't start, right-click on "View Full Image" and select "Save Image As..."
            </p>
          </>
        )}
      </Card>
      
      {/* Upgrade membership dialog */}
      <Dialog open={showUpgradeDialog} onOpenChange={setShowUpgradeDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-2xl font-bold">Upgrade to Remove Watermark</DialogTitle>
          </DialogHeader>
          
          <p className="text-center text-muted-foreground">Upgrade to unlock premium features and continue creating</p>
          
          <div className="py-6 space-y-6">
            <div className="flex items-center gap-4">
              <div className="bg-blue-50 rounded-full p-4">
                <svg className="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 6v12m-8-6h16" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold">More Credits</h3>
                <p className="text-muted-foreground">Generate more images with increased credit allowance</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="bg-purple-50 rounded-full p-4">
                <svg className="w-8 h-8 text-purple-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold">Priority Access</h3>
                <p className="text-muted-foreground">Get first access to new features and faster generation</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="bg-cyan-50 rounded-full p-4">
                <svg className="w-8 h-8 text-cyan-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 15a3 3 0 100-6 3 3 0 000 6z" />
                  <path d="M19 6H5a2 2 0 00-2 2v8a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold">No Watermarks</h3>
                <p className="text-muted-foreground">Remove watermarks from all generated images & videos</p>
              </div>
            </div>
          </div>
          
          <Button 
            onClick={() => window.location.href = '/pricing'} 
            className="w-full py-6 text-lg font-semibold"
            size="lg"
          >
            Upgrade to Premium
          </Button>
        </DialogContent>
      </Dialog>
      
      {/* Feature coming soon dialog */}
      <Dialog open={showComingSoonDialog} onOpenChange={setShowComingSoonDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-xl">Feature Coming Soon</DialogTitle>
            <DialogDescription className="text-center">
              Multi-image generation is under development. Stay tuned!
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-8 text-center">
            <div className="inline-flex h-20 w-20 items-center justify-center rounded-full bg-blue-100 mb-4">
              <svg className="h-10 w-10 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <p className="text-muted-foreground">
              We're working hard to bring you the ability to generate multiple images at once. 
              This feature will be available soon for premium users.
            </p>
          </div>
          
          <DialogFooter className="sm:justify-center">
            <Button 
              onClick={() => setShowComingSoonDialog(false)}
              className="w-full"
            >
              Got it!
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 