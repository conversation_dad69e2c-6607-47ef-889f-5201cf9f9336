# 水印逻辑最终版本

## 🎯 最终需求

- **积分 ≤ 5**：强制开启水印，用户无法控制，始终得到带水印的图片
- **积分 > 5**：默认关闭水印，用户可以选择开启，默认得到无水印的图片

## 📝 修改内容

### 1. 组件状态初始化修改

**修改文件：**
- `components/kontext/index.tsx`
- `components/kontextdev/index.tsx` 
- `components/generator4o/index.tsx`

**修改内容：**
```typescript
// 原来的逻辑
if (needsWatermark) {
  setShowWatermark(true);
  setIsUserPro(false);
} else {
  setIsUserPro(true);
  // 没有设置showWatermark状态
}

// 新的逻辑
if (needsWatermark) {
  setShowWatermark(true);
  setIsUserPro(false);
} else {
  // 积分充足的用户，默认关闭水印
  setShowWatermark(false);
  setIsUserPro(true);
}
```

### 2. 水印处理函数修改

**修改文件：** `utils/watermark.ts`

**参数：** `userWantsWatermark?: boolean`

**决策逻辑：**
```typescript
export async function processImagesWithWatermark(
  imageUrls: string[],
  taskId?: string,
  userWantsWatermark?: boolean
): Promise<string[]>
```

**处理流程：**
1. **积分 ≤ 5**：强制添加水印（忽略用户选择）
2. **积分 > 5 且 `userWantsWatermark === true`**：用户选择添加水印
3. **积分 > 5 且 `userWantsWatermark === false`**：返回原图（默认情况）
4. **积分 > 5 且 `userWantsWatermark === undefined`**：返回原图

### 3. 组件调用修改

**所有组件中的调用都更新为：**
```typescript
const processedImages = await processImagesWithWatermark(imageUrls, taskId, showWatermark);
```

## ✅ 最终行为

### 积分 ≤ 5 的用户
- ✅ 水印开关强制开启且被禁用
- ✅ 显示 "Upgrade" 按钮
- ✅ **始终得到带水印的图片**
- ✅ 无法关闭水印开关

### 积分 > 5 的用户
- ✅ **水印开关默认关闭**
- ✅ 可以自由控制水印开关
- ✅ **默认得到无水印的图片**
- ✅ 可以手动选择添加水印

### 未登录用户
- ✅ 水印开关强制开启且被禁用
- ✅ 显示 "Upgrade" 按钮
- ✅ 始终得到带水印的图片

## 🧪 测试场景

### 场景1：积分 ≤ 5 的用户
1. 登录后检查积分
2. 确认水印开关被强制开启且禁用
3. 生成图片，确认有水印
4. 尝试关闭水印开关，确认被阻止

### 场景2：积分 > 5 的用户
1. 登录后检查积分
2. 确认水印开关默认关闭
3. 生成图片，确认无水印
4. 手动开启水印开关
5. 再次生成图片，确认有水印

### 场景3：未登录用户
1. 确认水印开关被强制开启且禁用
2. 生成图片，确认有水印

## 🔧 技术实现

### 状态管理
- `showWatermark`: 控制UI开关状态
- `userNeedsWatermark`: 是否强制需要水印（积分 ≤ 5）
- `isUserPro`: 是否为付费用户（积分 > 5）

### 水印决策流程
1. 检查用户积分
2. 如果积分 ≤ 5：强制添加水印
3. 如果积分 > 5：根据用户选择决定
4. 错误情况：默认添加水印（安全优先）

## 📊 日志输出

更新后的日志信息：
- `🎨 User credits: X ≤ 5, watermark forced ON`
- `✅ User credits: X > 5, watermark OFF by default`
- `✅ User has sufficient credits and chose no watermark, returning original images`
- `✅ User has sufficient credits, returning original images (default no watermark)`

## 🚀 部署注意事项

1. 确保所有组件都使用新的水印处理逻辑
2. 测试不同积分用户的行为
3. 验证UI开关状态与实际处理结果一致
4. 确认错误处理和降级策略正常工作
