import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';

/**
 * 图片代理API，用于解决跨域下载问题
 * 接收图片URL作为查询参数，然后获取图片内容并返回
 */
export async function GET(request: NextRequest) {
  try {
    // 获取URL参数
    const url = request.nextUrl.searchParams.get('url');
    const download = request.nextUrl.searchParams.get('download') !== 'false'; // 默认为下载模式

    console.log('URL:', url?.substring(0, 100) + '...');
    console.log('Download mode:', download);

    if (!url) {
      return new NextResponse('Missing URL parameter', { status: 400 });
    }
    
    // 确保URL是有效的
    let imageUrl: string;
    try {
      imageUrl = decodeURIComponent(url);
      new URL(imageUrl); // 验证URL格式
    } catch (error) {
      console.error('Invalid image URL:', error);
      return new NextResponse('Invalid image URL', { status: 400 });
    }
    
    console.log('Start getting images:', imageUrl.substring(0, 100) + '...');
    
    // 获取图片内容
    const imageResponse = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      cache: 'no-store'
    });
    
    if (!imageResponse.ok) {
      console.error('Failed to fetch image:', imageResponse.status, imageResponse.statusText);
      return new NextResponse(`Failed to fetch image: ${imageResponse.status} ${imageResponse.statusText}`, { status: imageResponse.status });
    }
    
    // 获取图片数据
    const imageBuffer = await imageResponse.arrayBuffer();
    const contentType = imageResponse.headers.get('content-type') || 'image/jpeg';
    
    console.log('Image size:', imageBuffer.byteLength, 'bytes, type:', contentType);
    
    // 设置响应头
    const headers = new Headers();
    headers.set('Content-Type', contentType);
    headers.set('Content-Length', imageBuffer.byteLength.toString());

    if (download) {
      // 下载模式：指示浏览器下载文件
      headers.set('Content-Disposition', 'attachment; filename="restore-old-photos.com.png"');
      headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      headers.set('Pragma', 'no-cache');
      headers.set('Expires', '0');
    } else {
      // 显示模式：用于水印处理时的图片加载
      headers.set('Access-Control-Allow-Origin', '*');
      headers.set('Access-Control-Allow-Methods', 'GET');
      headers.set('Access-Control-Allow-Headers', 'Content-Type');
      headers.set('Cache-Control', 'public, max-age=31536000'); // 缓存1年
    }
    
    // 返回图片数据
    return new NextResponse(imageBuffer, {
      status: 200,
      headers
    });
  } catch (error) {
    console.error('Error proxying image:', error);
    return new NextResponse(`Error proxying image: ${(error as Error).message}`, { status: 500 });
  }
} 